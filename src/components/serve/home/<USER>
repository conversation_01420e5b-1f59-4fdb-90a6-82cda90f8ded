<script lang="ts">
import { message } from 'ant-design-vue';
import SwiperCore, { Navigation, Pagination } from 'swiper';
import 'swiper/modules/pagination/pagination.min.css';
import 'swiper/swiper-bundle.min.css';
import { Swiper, SwiperSlide } from 'swiper/vue/swiper-vue';
import { computed, defineComponent, onMounted, ref, toRefs, watch } from 'vue';
import { useRouter } from 'vue-router';
import { ServeUserActivitiesApi } from '../../../engines/iest/apis/serve/user/activities.api';
import { ServeActivityModel } from '../../../engines/iest/models/serve/activities';
import { VStore } from '../../../lib/vails/store/index';
import ComServeHomeCard from './ComServeHomeCard.vue';
import ComServeHomeHotIndex from './ComServeHomeHotIndex.vue';

SwiperCore.use([Pagination, Navigation]);

const ComServeHomeIndex = defineComponent({
  name: 'ComServeHomeIndex',
  components: {
    Swiper,
    SwiperSlide,
    ComServeHomeHotIndex,
    ComServeHomeCard,
  },
  props: {
    routerTab: {
      type: Object,
      required: true,
    },
  },
  setup(props) {
    const parents = [{ type: 'submodules', id: 1 }];
    const router = useRouter();
    //热点动态
    const hotActivityStore = new VStore(
      new ServeUserActivitiesApi({
        parents,
        params: {
          sub_q: {
            type_eq: 'Serve::ArticleActivity',
            state_eq: 'published',
            s: ['published_at desc'],
          },
        },
      }),
      ServeActivityModel,
    );
    // 轮播图
    const hotActivityStore2 = new VStore(
      new ServeUserActivitiesApi({
        parents,
        params: {
          sub_q: {
            cover_image_exists_true: 1,
            type_eq: 'Serve::ArticleActivity',
            state_eq: 'published',
            s: ['published_at desc'],
          },
        },
      }),
      ServeActivityModel,
    );
    //以案为鉴
    const alertActivityStore = new VStore(
      new ServeUserActivitiesApi({
        parents,
        params: {
          q: {
            state_eq: 'published',
            catalogs_id_eq: 3,
            is_hotted_true: 1,
          },
        },
      }),
    );
    //纪法小课
    const activityStore = new VStore(
      new ServeUserActivitiesApi({
        parents,
        params: {
          q: {
            state_eq: 'published',
            catalogs_id_eq: 2,
            is_hotted_true: 1,
          },
        },
      }),
      ServeActivityModel,
    );
    //应用场景-挂证取酬
    const scenceActivityStore = new VStore(
      new ServeUserActivitiesApi({
        parents,
        params: {
          q: {
            state_eq: 'published',
            catalogs_id_eq: 6,
            is_hotted_true: 1,
            s: ['published_at desc'],
          },
        },
      }),
      ServeActivityModel,
    );

    onMounted(() => {
      Promise.all([
        activityStore.index({
          per_page: 5,
        }),
        alertActivityStore.index({
          per_page: 15,
        }),
        hotActivityStore.index({
          per_page: 5,
        }),
        hotActivityStore2.index({
          per_page: 5,
        }),
      ]);
    });

    const onClickActivity = (record: any) => {
      if (record.model_payload?.link_to_origin === 'true' && record.model_payload?.link) {
        // 根据record的catalog_id来判断使用哪个store
        let targetStore;
        if (record.catalogs_id === 2) {
          targetStore = activityStore;
        } else if (record.catalogs_id === 3) {
          targetStore = alertActivityStore;
        } else {
          targetStore = hotActivityStore;
        }
        targetStore.find(record.id).then(() => {
          window.open(record.model_payload.link);
        });
      } else {
        const url = `/serve/home/<USER>/${record.id}`;
        router.push(url);
        props.routerTab.changeTabFollowByRouter(url);
      }
    };
    const goFiling = () => {
      window.open('https://beian.miit.gov.cn/');
    };

    const activeIndex = ref<number>(0);
    const activeItem = computed(() => {
      return data[activeIndex.value];
    });
    const showRecordIndex = ref(0);
    const visible = ref(false);
    const isModalLoading = ref(false);
    const data = [
      {
        url: 'https://stiei-obs2.obs.cn-east-2.myhuaweicloud.com/discipline-inspection/Group%206.png',
        activeUrl:
          'https://stiei-obs2.obs.cn-east-2.myhuaweicloud.com/discipline-inspection/Group%206%20active1.png',
        name: '短信',
        tag_id: 93,
        catalog_id: 6,
        intro: "警惕'挂证'兼职，严守纪律底线。",
      },
      {
        url: 'https://stiei-obs2.obs.cn-east-2.myhuaweicloud.com/discipline-inspection/Group%207.png',
        activeUrl:
          'https://stiei-obs2.obs.cn-east-2.myhuaweicloud.com/discipline-inspection/Group%207%20active1.png',
        name: '案例',
        tag_id: 92,
        catalog_id: 6,
        intro: '专业资格挂证乱象，揭秘行业违规行为。',
      },
      {
        url: 'https://stiei-obs2.obs.cn-east-2.myhuaweicloud.com/discipline-inspection/Group%2015.png',
        activeUrl:
          'https://stiei-obs2.obs.cn-east-2.myhuaweicloud.com/discipline-inspection/Group%2015%20active1.png',
        name: '视频',
        tag_id: 30,
        catalog_id: 6,
        isVideo: true,
        noBg: true,
        intro: '这些违规兼职取酬行为不能有！',
      },
      {
        url: 'https://stiei-obs2.obs.cn-east-2.myhuaweicloud.com/discipline-inspection/Group%2016.png',
        activeUrl:
          'https://stiei-obs2.obs.cn-east-2.myhuaweicloud.com/discipline-inspection/Group%2016%20active1.png',
        name: '海报',
        tag_id: 91,
        catalog_id: 6,
        noBg: true,
        intro: '',
      },
    ];
    const onHover = (index: number) => {
      activeIndex.value = index;
    };

    const openModal = (item: any) => {
      visible.value = true;
      isModalLoading.value = true;
      showRecordIndex.value = 0; // 重置索引
      scenceActivityStore
        .index({
          q: {
            s: ['id desc'],
            catalog_id_eq: item.catalog_id,
            tags_id_in: item.tag_id,
            state_eq: 'published',
          },
        })
        .then(() => {
          isModalLoading.value = false;
        });
    };

    const handleShowRecordIndex = (mode: number) => {
      if (isModalLoading.value) return message.warning('正在加载中,请稍后');
      if (mode > 0) {
        if (showRecordIndex.value >= scenceActivityStore.totalCount.value - 1) {
          message.warning('最后一条了');
          return;
        }
        showRecordIndex.value = showRecordIndex.value + 1;
        return;
      }

      if (showRecordIndex.value === 0) return message.warning('已经到头了');

      showRecordIndex.value = showRecordIndex.value - 1;
    };

    watch(
      () => visible.value,
      () => {
        showRecordIndex.value = 0;
      },
    );

    const getSrc = (record: any) => {
      if (!record) return '';
      const content = record.content?.content?.find((item: any) => item?.videos?.length > 0);
      return content?.videos?.[0]?.url || '';
    };

    return {
      ...toRefs(props),
      hotActivityStore,
      activityStore,
      records: activityStore.records,
      alerts: alertActivityStore.records,
      scenes: scenceActivityStore.records,
      onClickActivity,
      alertActivityStore,
      hotActivityStore2,
      goFiling,
      onHover,
      activeIndex,
      activeItem,
      data,
      visible,
      openModal,
      isModalLoading,
      showRecordIndex,
      handleShowRecordIndex,
      getSrc,
    };
  },
});
export default ComServeHomeIndex;
</script>

<template lang="pug">
.com-serve-home-index.flex.flex-col.items-center.w-1080px.pt-6.px-6
  ComServeHomeHotIndex.mb-6(:store='hotActivityStore',:store2='hotActivityStore2', :skipFunction='onClickActivity')
  section.w-full.mb-6
    .flex.mb-4
      .w-1.bg-blue-600.mr-4
      .text-gray-900.text-lg.font-medium 以案为鉴
    Swiper.swiper.w-full.h-108px(:slides-per-view='5', :space-between='16', :navigation='true')
      SwiperSlide.swiper-slide(v-for='alertBanner in alerts')
        img.cursor-pointer(
          :src='alertBanner.cover_image?.files?.[0]?.url || "#"',
          @click='onClickActivity(alertBanner)'
        )

  section.w-full.mb-6
    .flex.mb-4
      .w-1.bg-blue-600.mr-4
      .text-gray-900.text-lg.font-medium 纪法小课
    .flex.gap-x-4
      ComServeHomeCard(
        v-for='(record, index) in records',
        v-show='index < 4',
        @click='onClickActivity(record)',
        :record='record'
      )

  section.w-full.mb-12
    .flex.mb-4
      .w-1.bg-blue-600.mr-4
      .text-gray-900.text-lg.font-medium 专题场景-挂证取酬

    .flex.space-x-4
      .item.h-220px.bg-bottom.bg-cover.p-5.cursor-pointer(
        v-for='(item, i) in data',
        @mouseenter='onHover(i)',
        @click='openModal(item)',
        :style='`background-image:url(${activeIndex === i ? item.activeUrl : item.url})`',
        :class='{ item__hover: activeIndex === i }'
      )
        .text-lg.font-normal {{ item.name }}
        .text-base.font-normal(
          v-if='item?.intro',
          :style='`opacity:${activeIndex === i ? 0.7 : 0}`'
        ) {{ item.intro }}

  TaNoPaddingModal.ta-modal(
    v-model:visible='visible',
    :title='data?.[activeIndex]?.name',
    width='70vw',
    :modalStyle='{ height: "80vh" }'
  )
    .wrapper.w-full.h-full.flex.justify-center.items-center.px-14.overflow-y-auto
      a-spin(v-if='isModalLoading')
      .content__modal.h-full.w-full(v-else)
        .min-h-full.bg-cover.bg-center.flex.items-center.px-4(
          :style='`background-image:${activeItem.noBg ? "unset" : "url(https://stiei-obs2.obs.cn-east-2.myhuaweicloud.com/discipline-inspection/sceneBg.png)"}`'
        )
          TaVideo(
            v-if='activeItem.isVideo',
            :key='`video-${showRecordIndex}`',
            :src='getSrc(scenes?.[showRecordIndex])'
          )
          TaContentField(
            v-else,
            :value='scenes?.[showRecordIndex]?.content?.content',
            disabled='true'
          )
    template(#footer)
      .flex.items-center.justify-center.text-gray-500
        TaIcon.cursor-pointer(
          type='outline/chevron-left',
          class='!w-6 !h-6',
          @click='handleShowRecordIndex(-1)'
        )
        .text-base.pl-2.pr-4.cursor-pointer(@click='handleShowRecordIndex(-1)') 上一条
        .text-base.pl-4.pl-2.cursor-pointer(@click='handleShowRecordIndex(1)') 下一条
        TaIcon.cursor-pointer(
          type='outline/chevron-right',
          class='!w-6 !h-6',
          @click='handleShowRecordIndex(1)'
        )
  //- footer.w-full.p-8.flex.justify-between.items-center
  //-   .text-base.font-medium.text-gray-900.cursor-pointer(@click='goFiling') 版权所属: 中共杭州市滨江区纪律检查委员会、杭州市滨江区监察委员会
  //- .text-sm.font-normal.text-gray-500 备案证编号: 浙ICP*********号
</template>

<style lang="stylus" scoped>
.com-serve-home-index
  .swiper-slide img
    display block
    width 100%
    height 100%
    object-fit cover
  footer
    border-top 1px solid #E5E7EB
    background white
  .item
    width calc((100% / 5))
    transition all 500ms ease-in
    > div
      color black
      width 200px
  >>>.ck-content
    font-size 25px
  .item__hover
    flex-grow 1
    transition all 400ms ease-out
    > div
      color white
      transition all 350ms ease-out
</style>
