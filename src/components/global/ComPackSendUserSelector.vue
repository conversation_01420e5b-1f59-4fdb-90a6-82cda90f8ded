<script lang="ts">
import { ComUserRedisResourcesApi } from '@/apis/com/user/redis_resources.api';
import { ResMemberOrgUserApi } from '@/apis/res/member/org_user.api';
import { createSearchOptions } from '@/components/global/utils/filterOptions';
import { ServeManagePackSendUsersApi } from '@/engines/iest/serve-core/apis/serve/manage/users.api';
import { VObject, VStore } from '@/lib/vails';
import { computed, defineComponent, onMounted, ref, toRefs, watch } from 'vue';
import ComUserSelectorImport from './ComUserSelectorImport.vue';
import FilterPanel from './utils/FilterPanel.vue';
import SearchBox from './utils/SearchBox.vue';

// 防抖函数
const debounce = <T extends (...args: any[]) => any>(
  fn: T,
  delay: number,
): ((...args: Parameters<T>) => void) => {
  let timer: number | null = null;
  return function (this: any, ...args: Parameters<T>) {
    if (timer) {
      clearTimeout(timer);
    }
    timer = window.setTimeout(() => {
      fn.apply(this, args);
      timer = null;
    }, delay);
  };
};

const ComPackSendUserSelector = defineComponent({
  name: 'ComPackSendUserSelector',
  components: {
    SearchBox,
    FilterPanel,
    ComUserSelectorImport,
  },
  props: {
    value: { type: Array, default: () => [] },
    payload: { type: Object, default: () => ({}) },
    disabled: { type: Boolean, default: false },
  },
  setup(props, { emit }) {
    // 分页相关状态
    const currentPage = ref<number>(1);
    const pageSize = ref<number>(15);
    const total = ref<number>(0);

    // 搜索关键词
    const searchKeyword = ref<string>('');

    const localValue = computed({
      get: () => props.value,
      set: val => {
        emit('update:value', val);
      },
    });

    const localPayload = computed({
      get: () => props.payload,
      set: val => {
        emit('update:payload', val);
      },
    });

    // 初始化默认筛选条件
    if (!localPayload.value.payload) {
      localPayload.value.payload = {};
    }

    const packSendUsersApi = new ServeManagePackSendUsersApi({
      parents: [{ type: 'packs', id: localPayload.value.id }],
    });
    const loading = ref(false);
    const selectedUsers = ref<VObject[]>([]);

    // 用户表格配置项
    const userTableItems = [
      {
        name: '姓名',
        type: 'string',
        search: true,
        data_index: 'name',
      },
      {
        name: '账号',
        type: 'string',
        search: true,
        data_index: 'account',
      },
      {
        name: '岗位',
        type: 'string',
        data_index: 'member_pos_job',
      },
      {
        name: '部门',
        type: 'string',
        data_index: 'department_names',
      },
    ];

    // 用标签的表格配置项
    const tableItems = [
      {
        name: '名称',
        type: 'string',
        search: true,
        data_index: 'name',
      },
      {
        name: '',
        type: 'string',
        data_index: 'path_names',
      },
    ];

    // 引入字典数据
    const staffDictionary = ref(require('./utils/staff_dictionary.json'));

    // 搜索选项定义 - 从字典数据中获取
    const searchOptions = computed(() => {
      // 使用工厂函数生成搜索选项
      return createSearchOptions(staffDictionary.value);
    });

    // 添加高级搜索相关的状态
    const advancedSearchQuery = ref<Record<string, string | string[]>>({});

    // 定义查询接口
    interface AdvancedQuery {
      [key: string]: string | string[];
    }

    // 修改 ransack 计算属性，添加标签过滤条件
    const ransack = computed(() => {
      const baseRansack = {
        res_tags_id_in: localPayload.value.payload?.res_tag_ids || [],
        orgs_id_in: localPayload.value.payload?.org_ids || [],
        departments_id_in: localPayload.value.payload?.department_ids || [],
        ...advancedSearchQuery.value,
      };

      if (searchKeyword.value) {
        return {
          ...baseRansack,
          name_or_account_cont: searchKeyword.value,
        };
      }

      return baseRansack;
    });

    // 将ransack对象转换为ransackStr格式
    const ransackStr = computed(() => {
      try {
        // 创建一个新的对象专门用于TaApiField的ransackStr参数
        const ransackObj: Record<string, any> = {};

        // 处理高级筛选条件 - 包含各种标签选择项
        Object.keys(advancedSearchQuery.value).forEach(key => {
          const value = (advancedSearchQuery.value as Record<string, any>)[key];
          if (value !== null && value !== undefined && value !== '') {
            ransackObj[key] = value;
          }
        });

        // 处理部门和标签筛选
        if (localPayload.value.payload?.department_ids?.length > 0) {
          ransackObj.departments_id_in = localPayload.value.payload.department_ids;
        }

        // 添加标签筛选条件
        if (localPayload.value.payload?.res_tag_ids?.length > 0) {
          ransackObj.res_tags_id_in = localPayload.value.payload.res_tag_ids;
        }

        // 处理关键词搜索
        if (searchKeyword.value) {
          ransackObj.name_or_account_cont = searchKeyword.value;
        }

        // 确保ransackObj非空
        if (Object.keys(ransackObj).length === 0) {
          return '{}';
        }

        const result = JSON.stringify(ransackObj);

        return result;
      } catch (error) {
        console.error('[错误] 生成 ransackStr 失败:', error);
        return '{}';
      }
    });

    // 标签切换方法 - 修改为支持多选查询
    const toggleFilterTag = (key: string, value: string) => {
      const query = { ...advancedSearchQuery.value } as AdvancedQuery;

      // 确保键对应的值是数组
      if (!query[key]) {
        query[key] = [];
      } else if (!Array.isArray(query[key])) {
        // 如果之前是单值，转换为包含该值的数组
        query[key] = [query[key] as string];
      }

      // 获取当前数组
      const valueArray = query[key] as string[];

      // 切换值的选中状态
      const valueIndex = valueArray.indexOf(value);
      if (valueIndex > -1) {
        // 如果值已存在，移除它
        valueArray.splice(valueIndex, 1);
      } else {
        // 否则添加值
        valueArray.push(value);
      }

      // 如果数组为空，删除键
      if (valueArray.length === 0) {
        delete query[key];
      }

      // 使用防抖处理筛选条件变更
      debouncedFilterChange(query);
    };

    // 增强版防抖筛选条件变更处理
    const debouncedFilterChange = debounce((newQuery: AdvancedQuery) => {
      advancedSearchQuery.value = newQuery;
      // 重置到第一页
      currentPage.value = 1;
    }, 500); // 500毫秒延迟

    // // 检查是否有筛选条件
    // const hasFilters = computed(() => {
    //   return (
    //     Object.keys(advancedSearchQuery.value).length > 0 ||
    //     (localPayload.value.payload?.department_ids &&
    //       localPayload.value.payload.department_ids.length > 0) ||
    //     (localPayload.value.payload?.res_tag_ids &&
    //       localPayload.value.payload.res_tag_ids.length > 0) ||
    //     !!searchKeyword.value
    //   );
    // });

    // // 监听分页变化
    // watch(
    //   () => [currentPage.value, pageSize.value],
    //   ([newPage, newSize], [oldPage, oldSize]) => {
    //     // 只在值真正变化时触发加载，避免重复请求
    //     if (newPage !== oldPage || newSize !== oldSize) {
    //       if (localPayload.value?.id) {
    //         loadPackUsers(newPage);
    //       }
    //     }
    //   },
    // );

    // 加载包下的用户列表
    const loadPackUsers = async (page = currentPage.value) => {
      return;

      // if (!localPayload.value?.send_user_ids_redis_key) return;

      // const packId = localPayload.value?.id;
      // if (!packId) {
      //   console.log('未找到pack_id，无法加载用户列表');
      //   return;
      // }

      // loading.value = true;
      // try {
      //   await packSendUsersApi
      //     .index({
      //       q: {
      //         page: page,
      //         per_page: pageSize.value,
      //         ...ransack.value,
      //       },
      //     })
      //     .then(response => {
      //       const data = response.data;

      //       // 处理响应数据
      //       if (Array.isArray(data.data)) {
      //         selectedUsers.value = data.data as VObject[];
      //         localValue.value = data.data;

      //         // 处理分页元数据
      //         total.value = response.data.total_count || 0;
      //         currentPage.value = response.data.current_page || 1;
      //       } else {
      //         console.error('返回数据不是数组类型:', data);
      //       }
      //     })
      //     .catch(error => {
      //       console.error('加载包用户失败:', error);
      //     })
      //     .finally(() => {
      //       loading.value = false;
      //     });
      // } catch (error) {
      //   console.error('加载包用户时发生错误:', error);
      //   loading.value = false;
      // }
    };

    // 监听payload变化时重新加载用户
    // watch(
    //   () => localPayload.value?.id,
    //   newPackId => {
    //     if (newPackId) {
    //       // 重置分页到第一页
    //       currentPage.value = 1;
    //       loadPackUsers(1);
    //     }
    //   },
    // );

    // 监听搜索关键词变化
    // watch(
    //   () => searchKeyword.value,
    //   (newKeyword, oldKeyword) => {
    //     if (newKeyword !== oldKeyword) {
    //       // 重置到第一页
    //       currentPage.value = 1;

    //       if (localPayload.value?.id) {
    //         loadPackUsers(1);
    //       }
    //     }
    //   },
    //   { flush: 'post' }, // 使用flush: 'post'替代debounce
    // );

    // 添加对筛选条件的监听
    // watch(
    //   () => [
    //     localPayload.value?.payload?.org_ids,
    //     localPayload.value?.payload?.department_ids,
    //     localPayload.value?.payload?.res_tag_ids,
    //   ],
    //   () => {
    //     // 重置到第一页
    //     currentPage.value = 1;
    //   },
    //   { deep: true },
    // );

    // 监听ransackStr变化，确保筛选条件更新时发送正确的请求
    // watch(
    //   ransackStr,
    //   (newRansackStr, oldRansackStr) => {
    //     if (newRansackStr !== oldRansackStr) {
    //       // 如果有筛选条件，在这里模拟触发TaApiField的刷新
    //       if (Object.keys(JSON.parse(newRansackStr)).length > 0) {
    //         // 检查当前是否有pack_id和是否需要显式调用loadPackUsers
    //         if (localPayload.value?.id) {
    //           // 判断是否只有简单搜索关键词筛选，此情况下需要显式调用loadPackUsers
    //           const jsonObj = JSON.parse(newRansackStr);
    //           const onlyHasKeywordSearch =
    //             Object.keys(jsonObj).length === 1 &&
    //             jsonObj.name_or_account_cont &&
    //             Object.keys(advancedSearchQuery.value).length === 0 &&
    //             !localPayload.value?.payload?.department_ids?.length &&
    //             !localPayload.value?.payload?.res_tag_ids?.length;

    //           if (onlyHasKeywordSearch) {
    //             loadPackUsers(1);
    //           }
    //         }
    //       }
    //     }
    //   },
    //   { immediate: true },
    // );

    // 组件挂载时加载包用户
    onMounted(() => {
      if (localPayload.value?.id) {
        loadPackUsers();
      } else {
        console.log('组件挂载时没有pack_id，跳过加载用户');
      }
    });
    // 分页控制方法
    const handlePageChange = (page: number) => {
      currentPage.value = page;

      // 触发刷新数据
      if (localPayload.value?.id) {
        loadPackUsers(page);
      }
    };

    const handleSizeChange = (current: number, size: number) => {
      // 当每页显示数量变化时，检查是否需要调整当前页
      if (size !== pageSize.value) {
        const newMaxPage = Math.ceil(total.value / size);
        // 如果新的页码超出最大页码，则自动调整到最大页码
        if (current > newMaxPage && newMaxPage > 0) {
          current = newMaxPage;
        }
      }

      pageSize.value = size;
      currentPage.value = current;

      // 触发刷新数据
      if (localPayload.value?.id) {
        loadPackUsers(current);
      }
    };

    // 处理搜索输入 (添加防抖)
    const debouncedSearch = debounce((value: string) => {
      searchKeyword.value = value;
    }, 300);

    const handleSearchInput = (e: Event) => {
      const target = e.target as HTMLInputElement;
      debouncedSearch(target.value);
    };

    // 清空搜索
    const clearSearch = () => {
      searchKeyword.value = '';
    };

    // 更新部门IDs
    const updateDepartmentIds = (value: any[]) => {
      if (!localPayload.value.payload) {
        localPayload.value.payload = {};
      }
      localPayload.value.payload.department_ids = value;

      // 重置到第一页
      currentPage.value = 1;
    };

    // 添加加载数据的方法，整合所有筛选条件
    const reloadData = (page = 1) => {
      currentPage.value = page;
      if (localPayload.value?.id) {
        loadPackUsers(page);
      }
    };

    // 为TaApiField提供额外的配置，确保筛选条件正确传递
    const extraConfig = computed(() => {
      try {
        // 解析ransackStr得到筛选对象
        const queryObj = JSON.parse(ransackStr.value);

        return {
          params: {
            q: queryObj,
          },
          // 强制每次筛选条件变化时都会重新获取数据
          pagination: {
            current: 1,
            // 如果直接使用currentPage变化会导致循环依赖
          },
        };
      } catch (error) {
        // console.error('[错误] 解析ransackStr失败:', error);
        return {
          params: {
            q: {},
          },
        };
      }
    });

    const handleTaApiFieldOpen = () => {
      // console.log('[调试] TaApiField打开, ransackStr内容:', ransackStr.value);
    };

    // 清空所有筛选条件
    function handleClearAllFilters() {
      // 清空部门和标签
      if (localPayload.value.payload) {
        localPayload.value.payload.department_ids = [];
        // 移除标签清空
        localPayload.value.payload.res_tag_ids = [];
        // 清空所有高级筛选条件
        for (const key in advancedSearchQuery.value) {
          if (Object.prototype.hasOwnProperty.call(advancedSearchQuery.value, key)) {
            advancedSearchQuery.value[key] = Array.isArray(advancedSearchQuery.value[key])
              ? []
              : '';
          }
        }
      }
    }

    const importStore = new VStore(new ResMemberOrgUserApi());
    const redisResourcesStore = new VStore(new ComUserRedisResourcesApi());

    const manualStore = new VStore(new ResMemberOrgUserApi());
    const manualRedisSourcesApi = new ComUserRedisResourcesApi();

    const redisResourcesConfig = computed(() => ({
      store: redisResourcesStore,
      params: {
        redis_key: localPayload.value.send_user_ids_redis_key,
        resources_class: 'User',
      },
      mode: 'table',
      actions: [{ key: 'delete', enabled: !props.disabled }],
      table: {
        columns: [
          { title: '姓名', dataIndex: 'name' },
          { title: '账号', dataIndex: 'account' },
          { title: '岗位', dataIndex: 'member_pos_job' },
          { title: '部门', dataIndex: 'department_names' },
        ],
      },
    }));

    const validate = () => {
      if (localPayload.value.send_user_ids_redis_key) {
        return redisResourcesStore.records.value.length > 0
          ? Promise.resolve()
          : Promise.reject('请先导入用户');
      } else {
        return Promise.resolve();
      }
    };

    // 在setup中添加updateResTagIds方法
    const updateResTagIds = (value: any[]) => {
      if (!localPayload.value.payload) {
        localPayload.value.payload = {};
      }
      localPayload.value.payload.res_tag_ids = value;

      // 重置到第一页并触发数据重新加载
      currentPage.value = 1;
      if (localPayload.value?.id) {
        loadPackUsers(1);
      }
    };

    const onIndex = (data: VObject) => {
      localPayload.value.send_user_ids_redis_key = data.redis_key;
      localPayload.value.send_user_ids_redis_key_refresh = data.redis_key;
    };

    return {
      ...toRefs(props),
      localValue,
      localPayload,
      loading,
      selectedUsers,
      loadPackUsers,
      userTableItems,
      tableItems,
      ransack,
      ransackStr,
      currentPage,
      pageSize,
      total,
      handlePageChange,
      handleSizeChange,
      searchKeyword,
      handleSearchInput,
      clearSearch,
      searchOptions,
      advancedSearchQuery,
      toggleFilterTag,
      updateDepartmentIds,
      reloadData,
      // hasFilters,
      extraConfig,
      handleTaApiFieldOpen,
      handleClearAllFilters,
      importStore,
      redisResourcesStore,
      redisResourcesConfig,
      validate,
      updateResTagIds,
      onIndex,
      manualStore,
      manualRedisSourcesApi,
    };
  },
});
export default ComPackSendUserSelector;
</script>

<template lang="pug">
.com-pack-send-user-selector.flex.flex-col
  .user-selector-container.flex-1.overflow-auto
    TaIndexView(:config='redisResourcesConfig', @onIndex='onIndex')
      template(#header)
        .flex.items-center.justify-between(v-if='redisResourcesStore.totalCount.value >= 0')
          | 已选中 {{ redisResourcesStore.totalCount }} 位用户
      template(#actions='{ actions }')
        .flex.space-x-2.items-center
          ComUserSelectorManual(
            v-if='!disabled',
            :redisKey='localPayload.send_user_ids_redis_key',
            :manualStore='manualStore',
            :manualRedisSourcesApi='manualRedisSourcesApi',
            :query='JSON.parse(ransackStr)',
            @refresh='actions.refresh'
          )
            template(#actions)
              FilterPanel.min-h-15.max-h-100.overflow-auto(
                :localPayload='localPayload',
                :disabled='disabled',
                :tableItems='tableItems',
                :searchOptions='searchOptions',
                :advancedSearchQuery='advancedSearchQuery',
                @toggle-filter-tag='toggleFilterTag',
                @update:department-ids='updateDepartmentIds',
                @update:res-tag-ids='updateResTagIds',
                @clear-all-filter-tags='handleClearAllFilters'
              )
          ComUserSelectorImport(
            v-if='!disabled',
            v-model:value='localPayload.send_user_ids_redis_key',
            :store='importStore'
            @success='actions.refresh'
          )
            //- SearchBox(v-model='searchKeyword', @input='handleSearchInput', @clear='clearSearch')
</template>

<style lang="stylus" scoped>
.com-pack-send-user-selector
  width 100%
  height 100%
  .filter-section
    margin-bottom 16px
    padding 8px 0
    background-color #f9f9f9
    border-radius 4px
  .user-selector-container
    display flex
    flex-direction column
    height 100%
  .search-input
    width 240px
    margin-right 16px
  .filter-tag
    margin 4px
    cursor pointer
  .filter-tag-selected
    color #1890ff
    background-color #e6f7ff
    border-color #91d5ff
  .table-footer
    display flex
    align-items center
    justify-content flex-end
    margin-top 16px
  :deep(.ant-modal)
    top 5vh
    width 95% !important
    max-width 1600px !important
    height 90vh
    .ant-modal-body
      max-height 70vh
      padding 12px 24px
      overflow-y auto
    .ant-modal-content
      display flex
      flex-direction column
      height 100%
    .ant-modal-header
      padding 12px 24px
      border-bottom 1px solid #f0f0f0
    .ant-table-wrapper
      flex 1
      min-height 300px
      overflow auto
    .ant-pagination
      margin 16px 0
</style>
