<template lang="pug">
.screen_bubble 
  .screen_bubble_wrapper.w-full.h-full.relative
    .screen_bubble_item.cursor-pointer(
      v-for="(item, index) in bubbleList",
      :class='`screen_bubble_${index + 1}`'
      @click="check"
    ) {{ item.name }}
      .flex.items-baseline
        text.value-font {{ item.value }}
        text.mini-font {{ item.extra }}

    //- //- 电子信息
    //- .screen_bubble_item.screen_bubble_1.w-24.h-24(@click="check") 电子信息
    //-   .mini-font (1,460)
    //- //- 生物医疗与医疗器械
    //- .screen_bubble_item.screen_bubble_2.w-24.h-24(@click="check") 机械装备
    //-   .mini-font (2,364)
    //- //- 现代农业
    //- .screen_bubble_item.screen_bubble_3.w-16.h-16(@click="check") 交通运输
    //-   .mini-font (948)
    //- //- 先进制造
    //- .screen_bubble_item.screen_bubble_4.w-21.h-21(@click="check") 轻工制造
    //-   .mini-font (1,831)
    //- //- 文化创意
    //- .screen_bubble_item.screen_bubble_5.w-16.h-16(@click="check") 医疗健康
    //-   .mini-font (818)
    //- //- 环境保护
    //- .screen_bubble_item.screen_bubble_6.w-21.h-21(@click="check") 建筑工程 
    //-   .mini-font (1,159)
    //- //- 新材料
    //- .screen_bubble_item.screen_bubble_7.w-21.h-21(@click="check") 能源动力
    //-   .mini-font (1,224)
    //- //- 现代交通
    //- .screen_bubble_item.screen_bubble_8.w-16.h-16(@click="check") 材料
    //-   .mini-font (1,045)
    //- //- 新能源与节能
    //- .screen_bubble_item.screen_bubble_9.w-24.h-24(@click="check") 仪器仪表
    //-   .mini-font (1,638)
    //- //- 其他
    //- .screen_bubble_item.screen_bubble_10.w-16.h-16(@click="check") 通讯
    //-   .mini-font (796)
</template>
<script lang="ts">
import { computed, defineComponent, toRefs } from 'vue';

export default defineComponent({
  name: 'ComBubbleFix',
  props: {
    data: { type: Object, required: true },
  },
  setup(props) {
    const bubbleList = computed(() => {
      return props.data?.[0].seriesData;
    });
    // const { onClick: onPreviewClick } = usePreviewClickable(props);
    // const check = (e: Event) => {
    //   const target = e.target as HTMLElement;
    //   console.log(target.innerText);
    //   onPreviewClick(
    //     {
    //       mode: 'page_setting',
    //       offset_top: '0px',
    //       page_setting_id: 4,
    //     },
    //     {
    //       type: target.innerText,
    //     },
    //   );
    // };
    const check = () => {};
    return {
      ...toRefs(props),
      check,
      bubbleList,
    };
  },
});
</script>
<style lang="stylus">
@keyframes mymove{
  0% {
    transform: translate(0px, 0px);
  }
  50% {
    transform: translate(0px, -5px);
  }
	100% {
    transform: translate(0px, 0px);
  }

}
@keyframes mymove1{
  0% {
    transform: translate(0px, 0px);
  }
  50% {
    transform: translate(5px, 0px);
  }
	100% {
    transform: translate(0px, 0px);
  }

}
@keyframes mymove2{
  0% {
    transform: translate(0px, 0px);
  }
  50% {
    transform: translate(0px, 5px);
  }
	100% {
    transform: translate(0px, 0px);
  }

}
@keyframes mymove3{
  0% {
    transform: translate(0px, 0px);
  }
  50% {
    transform: translate(-5px, 0px);
  }
	100% {
    transform: translate(0px, 0px);
  }

}

.screen_bubble
  width 453px
  height 210px
  .value-font
    width 100%
    font-size: 14px
    transform: scale(1)
    line-height 18px
    text-align center
    white-space nowrap
  .mini-font
    width 100%
    font-size: 12px
    transform: scale(0.83)
    line-height 12px
    text-align center
    white-space nowrap
  .screen_bubble_wrapper
    .screen_bubble_item
      position absolute
      background-size cover
      background-position 50%
      background-repeat no-repeat
      padding 2px
      display flex
      flex-direction column
      align-items center
      justify-content center
      text-align center
      justify-content center
      animation-timing-function: ease;
      cursor pointer
    .screen_bubble_1
      top 0px
      left 81px
      font-size 14px
      font-weight 500
      animation: mymove2 5.4s infinite
      width 96px
      height 96px
    .screen_bubble_2
      left 189px
      top 14.78px
      padding 0 10px
      animation: mymove1 4.5s infinite
      width 96px
      height 96px
    .screen_bubble_3
      left 303px
      top 10px
      font-weight: 500
      font-size: 12px
      animation: mymove 3.8s infinite
      width 64px
      height 64px
    .screen_bubble_4
      top 59px
      left 0px
      font-size 14px
      font-weight 500
      animation: mymove1 6.1s infinite
      width 84px
      height 84px
    .screen_bubble_5
      top 91px
      left 141px
      font-weight: 500
      font-size: 12px
      animation: mymove3 4.77s infinite
      width 64px
      height 64px
    .screen_bubble_6
      top 76px
      left 271px
      font-size 14px
      font-weight 500
      animation: mymove2 5.13s infinite
      width 84px
      height 84px
    .screen_bubble_7
      top 43px
      right 0px
      font-weight: 500
      font-size: 14px
      animation: mymove 4.18s infinite
      width 84px
      height 84px
    .screen_bubble_8
      left 81px
      bottom 28px
      font-size 12px
      font-weight 500
      animation: mymove1 4.96s infinite
      width 64px
      height 64px
    .screen_bubble_9
      bottom 0px
      left 190px
      padding 0 22px
      font-size 13px
      font-weight 500
      animation: mymove3 4.69s infinite
      width 96px
      height 96px
    .screen_bubble_10
      right 27px
      bottom 23px
      font-size 12px
      font-weight 500
      animation: mymove2 3.99s infinite
      width 64px
      height 64px
    .screen_bubble_2,.screen_bubble_4
      background-image url(https://oss.innomatch.net/static-assets/kechaungbao/screen/orange.png)
      color #FEB076
    .screen_bubble_1,.screen_bubble_7,.screen_bubble_9
      background-image url(https://oss.innomatch.net/static-assets/kechaungbao/screen/green.png)
      color: #12E8DF
    .screen_bubble_3,.screen_bubble_5,.screen_bubble_10
      background-image url(https://oss.innomatch.net/static-assets/kechaungbao/screen/blue.png)
      color: #2AB1FD
    .screen_bubble_6,.screen_bubble_8
      background-image url(https://oss.innomatch.net/static-assets/kechaungbao/screen/purple.png)
      color: #E275FE
</style>
