<template lang="pug">
.w-full.h-full
  .com_spin_round_base
    .w-full.h-full.bg-cover.bg-center(:class='`coup_${coups - 2}`')
      img.w-4.h-4.absolute(v-for='(item,index) in data', :src='finalIcon[index]' :class='`img_coup_${index + 1}`')
      .text-sm.w-24.absolute.h-5(v-for='(item,index) in data' :class='`text_coup_${index + 1}`')
        .w-full.flex.items-center.h-full.justify-end(v-if='isLeft(index,coups)')
          .text-white.text-sm.font-bold.mr-2.dingtext {{ item.seriesData.toLocaleString() }}
          .text-white.text-sm {{ item.seriesName }}
        .w-full.flex.items-center.h-full.justify-start(v-else)
          .text-white.text-sm {{ item.seriesName }}
          .text-white.text-sm.font-bold.ml-2.dingtext {{ item.seriesData.toLocaleString() }}
</template>
<script lang="ts">
import { PropType, computed, defineComponent, toRefs } from 'vue';
const ComSpinRoundBase = defineComponent({
  name: 'ComSpinRoundBase',
  components: {},
  props: {
    data: {
      type: Array as PropType<{ name: string; value: number }[]>,
      default: () => [
        {
          seriesName: '专利数',
          seriesData: 1984,
        },
        {
          seriesName: '著作权',
          seriesData: 1984,
        },
        {
          seriesName: '商标数',
          seriesData: 1984,
        },
        {
          seriesName: '商标数',
          seriesData: 1984,
        },
        {
          seriesName: '商标数',
          seriesData: 1984,
        },
        {
          seriesName: '商标数',
          seriesData: 1984,
        },
      ],
    },
    iconList: {
      type: Array as PropType<string[]>,
      default: () => [],
    },
  },
  setup(props) {
    const coups = computed(() => props.data.length);
    const v_iconlist = [
      'https://oss.innomatch.net/static-assets/screen/component/book-open.png',
      'https://oss.innomatch.net/static-assets/screen/component/user-group%20(1).png',
      'https://oss.innomatch.net/static-assets/screen/component/document-text.png',
      'https://oss.innomatch.net/static-assets/screen/component/document-text.png',
      'https://oss.innomatch.net/static-assets/screen/component/document-text.png',
      'https://oss.innomatch.net/static-assets/screen/component/document-text.png',
    ];
    const finalIcon = computed(() => {
      return [...props.iconList, ...v_iconlist];
    });
    const isLeft = (index: number, coups: number) => {
      if (coups === 3 && index === 0) return true;
      if ((coups === 4 || coups === 5) && (index === 0 || index === 1)) return true;
      if (coups === 6 && (index === 0 || index === 1 || index === 2)) return true;
      return false;
    };
    return {
      ...toRefs(props),
      coups,
      finalIcon,
      isLeft,
    };
  },
});
export default ComSpinRoundBase;
</script>
<style lang="stylus" scoped>
.com_spin_round_base
  position relative
  width 484px
  height 240px
  .dingtext
    font-family 'DINCondensedBold'
    position relative
    top 2px
  .coup_1
    background-image url(https://oss.innomatch.net/static-assets/screen/component/spin_round_default_1.png)
    .img_coup_1
      top 103px
      left 121px
    .img_coup_2
      top 43px
      left 231px
    .img_coup_3
      top 123px
      left 342px
    .text_coup_1
      top 99px
      right 379px
    .text_coup_2
      top 39px
      left 263px
    .text_coup_3
      top 120px
      left 374px
  .coup_2
    background-image url(https://oss.innomatch.net/static-assets/screen/component/spin_round_default_2.png)
    .img_coup_1
      top 143px
      left 117px
    .img_coup_2
      top 60px
      left 146px
    .img_coup_3
      top 51px
      left 263px
    .img_coup_4
      top 133px
      left 343px
    .text_coup_1
      top 139px
      right 383px
    .text_coup_2
      top 56px
      right 354px
    .text_coup_3
      top 47px
      left 295px
    .text_coup_4
      top 130px
      left 375px
  .coup_3
    background-image url(https://oss.innomatch.net/static-assets/screen/component/spin_round_default_3.png)
    .img_coup_1
      top 143px
      left 117px
    .img_coup_2
      top 73px
      left 136px
    .img_coup_3
      top 32px
      left 232px
    .img_coup_4
      top 89px
      left 334px
    .img_coup_5
      top 153px
      left 356px
    .text_coup_1
      top 139px
      right 383px
    .text_coup_2
      top 69px
      right 364px
    .text_coup_3
      top 28px
      left 264px
    .text_coup_4
      top 86px
      left 366px
    .text_coup_5
      top 150px
      left 388px
  .coup_4
    background-image url(https://oss.innomatch.net/static-assets/screen/component/spin_round_default_4.png)
    .img_coup_1
      top 153px
      left 117px
    .img_coup_2
      top 89px
      left 116px
    .img_coup_3
      top 31px
      left 184px
    .img_coup_4
      top 30px
      left 270px
    .img_coup_5
      top 89px
      left 334px
    .img_coup_6
      top 153px
      left 356px
    .text_coup_1
      top 149px
      right 383px
    .text_coup_2
      top 85px
      right 384px
    .text_coup_3
      top 27px
      right 316px
    .text_coup_4
      top 26px
      left 302px
    .text_coup_5
      top 86px
      left 366px
    .text_coup_6
      top 150px
      left 388px
</style>
