<template lang="pug">
.com_funnel_fix.relative.w-full.h-full
  .absolute.bg-cover.bg-center(class="left-[9px] top-[29px] w-[467px] h-[466px]", style="background-image: url('https://oss.innomatch.net/static-assets/cujin/screen/analysis_bg.png')")
  //- .absolute.bg-cover.bg-center(class="left-[126px] -top-[6px] w-[210px] h-[64px]", style="background-image: url('https://oss.innomatch.net/static-assets/cujin/screen/analysis_card_lg.png')")
  .absolute.item-border-outer(class="left-[126px] -top-[6px] w-[210px] h-[64px]",  style="padding: 1px;min-width: 40px;") 
    .item-border-inner.w-full.h-full.bg-cover.bg-center.flex.flex-row.items-center(style="padding: 8px 8px 4px 16px;")
      img.w-7.mr-3.ml-4(class="h-[32px]",src="https://oss.innomatch.net/static-assets/cujin/screen/analysis_tree_icon.png")
      .flex.flex-col.flex-1
        .text-white.text-sm.font-normal {{ analysisData[0]?.name }}
        .flex.flex-row.items-baseline 
          .text-cyan-400.text-2xl.font-bold.font-DINAlternate(class="px-[6px]") {{ analysisData[0]?.value }}
          .text-white.text-sm.font-normal 个
  template(v-for="(item,index) in analysisData")
    .absolute.item-border-outer(class="h-[48px]", :style="{left: `${item.x}px`,top: `${item.y}px`}" style="padding: 1px;min-width: 40px;",v-if="index > 0") 
      .item-border-inner.w-full.h-full.bg-cover.bg-center.flex.flex-row.items-baseline(style="padding: 8px 18px 12px 16px;")
        .text-white.text-sm.font-normal {{item.name}}
        .text-cyan-400.text-2xl.font-bold.font-DINAlternate(class="px-[6px]") {{item.value}}
        .text-white.text-sm.font-normal {{item.extra}}
</template>
<script lang="ts">
import { PropType, computed, defineComponent, toRefs } from 'vue';

const ComFunnelFix = defineComponent({
  name: 'ComFunnelFix',
  components: {},
  props: {
    data: {
      type: Array,
      default: () => [],
    },
  },
  setup(props) {
    const analysisData = computed(() => {
      return (props.data[0] as any)?.seriesData;
    });
    return {
      ...toRefs(props),
      analysisData,
    };
  },
});
export default ComFunnelFix;
</script>
<style lang="stylus" scoped>
.com_funnel_fix
  .font-DINAlternate
    font-family: DIN Alternate;
  .item-border-outer
    border-radius: 4px;
    background: linear-gradient(216.66deg, rgba(4, 201, 189, 0.3) 19.92%, #04C9BD 48.94%, rgba(23, 233, 245, 0.4) 77.99%);
    box-shadow: 0px 0px 6px 0px #03E3FF33;
    box-shadow: 0px 0px 12px 0px #03F7E85C inset;
  .item-border-inner
    background-image: url('https://oss.innomatch.net/static-assets/cujin/screen/anaylysis_card_mask_bg.png')
    background-color: #012936;
    border-radius: 4px;
</style>
