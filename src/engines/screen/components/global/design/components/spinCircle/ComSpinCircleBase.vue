<template lang="pug">
.w-full.h-full
  .com_spin_circle_base
    .inner_cicle.bg-cover.bg-center
    template(v-if='coups >= 3 && coups <= 6')
      .w-full.h-full.relative(:class='`coup_${coups - 2}`')
        .w-24.h-19.absolute(v-for='(record, index) in data', :class='`f_${index + 1}`')
          .w-full.flex.justify-center
            img(:src='finalIconList[index]')
          .flex.flex-row.items-center.text-white.relative.justify-center(
            class='top-[-20px]',
            :style='{ color: finalColorList[index] }'
          )
            .text-sm.font-semibold.mr-1.flex-shrink-0 {{ record.seriesName }}
            .text-sm.font-bold.f_value {{ record.seriesData.toLocaleString() }}
            .text-sm.font-bold.flex-shrink-0(v-if='record.unit') {{ record.unit }}
</template>
<script lang="ts">
import { PropType, computed, defineComponent, toRefs } from 'vue';
const ComSpinCircleBase = defineComponent({
  name: 'ComSpinCircleBase',
  components: {},
  props: {
    data: {
      type: Array as PropType<{ name: string; value: number }[]>,
      default: () => [],
    },
    iconList: {
      type: Array as PropType<string[]>,
      default: () => [],
    },
    customColor: {
      type: Array as PropType<string[]>,
      default: () => [],
    },
  },
  setup(props) {
    const coups = computed(() => props.data.length);
    const list = [
      'https://oss.innomatch.net/static-assets/screen/component/default_circle_ellipse_1.png',
      'https://oss.innomatch.net/static-assets/screen/component/default_circle_ellipse_2.png',
      'https://oss.innomatch.net/static-assets/screen/component/default_circle_ellipse_3.png',
      'https://oss.innomatch.net/static-assets/screen/component/default_circle_ellipse_3.png',
      'https://oss.innomatch.net/static-assets/screen/component/default_circle_ellipse_3.png',
      'https://oss.innomatch.net/static-assets/screen/component/default_circle_ellipse_3.png',
    ];
    const c_list = ['#8DFCB5', '#FEB076', '#2AB1FD', '#DE7070', '#2AB1FD', '#718AF7'];
    const finalIconList = computed(() => {
      return [...props.iconList, ...list];
    });
    const finalColorList = computed(() => {
      return [...props.customColor, ...c_list];
    });
    return {
      ...toRefs(props),
      coups,
      finalIconList,
      finalColorList,
    };
  },
});
export default ComSpinCircleBase;
</script>
<style lang="stylus" scoped>
.com_spin_circle_base
  width 460px
  height 164px
  position relative
  .f_value
    font-family 'DINCondensedBold'
    font-weight 700
    font-size 18px
    position relative
    top 1px
  .inner_cicle
    position absolute
    width 376px
    height 101px
    left 41px
    top 28.82px
    background-image url('https://oss.innomatch.net/static-assets/screen/component/circle_ellipse.png') // innomatchoss.oss-cn-hangzhou.aliyuncs.com/screen/component/circle_ellipse.png)
  .coup_1
    position relative
    .f_1
      top 77px
      left 182px
    .f_2
      top 0px
      left 68px
    .f_3
      top 0px
      right 68px
  .coup_2
    .f_1
      top 72px
      left 232px
    .f_2
      top 64px
      left 50px
    .f_3
      top -16px
      left 135px
    .f_4
      top 0px
      right 32px
  .coup_3
    .f_1
      top 75px
      left 157px
    .f_2
      top 35px
      left -9px
    .f_3
      top -16px
      left 93px
    .f_4
      top -16px
      right 103px
    .f_5
      top 67px
      right 50px
  .coup_4
    .f_1
      top 75px
      left 128px
    .f_2
      top 39px
      left -8px
    .f_3
      top -16px
      left 102px
    .f_4
      top -16px
      right 119px
    .f_5
      top 20px
      right -8px
    .f_6
      top 80px
      right 111px
</style>
