<template lang="pug">
.com_comm_title
  .flex.items-center(class="h-[30px]",:class='extraPos==="left" ? "" : "justify-between"')
    .flex.items-center
      img.mr-2(class="w-[10px] h-[20px]", src="https://oss.innomatch.net/static-assets/cujin/screen/title_icon.png")
      .title(:style="{fontSize: `${titleSize}px`}") {{ title }}
      ComTooltip(v-if="tootTipContent", :title="tootTipContent")
    slot(name = 'extra')
  .blank-line.mb-2






</template>
<script lang="ts">
import { computed, defineComponent, toRefs, ref } from 'vue';

export default defineComponent({
  name: 'ComCujinTitle',
  props: {
    title: { type: Object, required: true },
    titleSize: { type: Number, required: false, default: 16 },
    extraPos: { type: String, default: '' },
    tootTipContent: { type: String, default: '' },
  },
  setup(props) {
    return {
      ...toRefs(props),
    };
  },
});
</script>
<style lang="stylus">
.com_comm_title

  .blank-line
    margin-top 2px
    border-bottom: 1px solid;
    border-image-source linear-gradient(90deg, rgba(0, 225, 255, 0.2) 0%, rgba(42, 91, 253, 0.2) 51.04%, rgba(42, 91, 253, 0) 100%)
    border-image-slice 1

  .title
    font-family PingFang SC
    font-weight 500
    line-height 24px
    letter-spacing 0em
    text-align left
    background-image: linear-gradient(180deg, #FFFFFF 0%, #00E1FF 100%);
    -webkit-background-clip: text;
    background-clip: text;
    color: transparent;
</style>
