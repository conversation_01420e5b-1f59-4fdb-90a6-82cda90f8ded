<template lang="pug">
.com_keyou_title.w-full.relative(class="h-[38px]")
  .flex.justify-between.items-center
    .text-white.text-lg.font-medium.ml-4 {{ title }}
    slot(name='rightContent')
  img.absolute.bottom-0.h-5.w-full(:src='titleImg')
</template>
<script lang="ts">
import { ref, defineComponent, reactive, toRefs } from 'vue';
export default defineComponent({
  name: 'ComKeYouGroupTitle',
  props: {
    title: {
      type: String,
      default: () => '',
    },
  },
  setup(props) {
    const infos = reactive({
      titleImg:
        'https://oss.innomatch.net/static-assets/keyou/screen/group_title_bottom_decoration.png',
    });
    return {
      ...toRefs(props),
      ...toRefs(infos),
    };
  },
});
</script>
<style lang="stylus" scoped>
// TODO
</style>
