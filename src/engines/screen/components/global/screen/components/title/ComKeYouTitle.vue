<template lang="pug">
.com_keyou_title.w-full.relative(class="h-[47px]")
  .text-white.text-2xl.font-medium.ml-4 {{ title }}
  img.absolute.bottom-0.h-5.w-full(:src='titleImg')
</template>
<script lang="ts">
import { ref, defineComponent, reactive, toRefs } from 'vue';
export default defineComponent({
  name: 'ComKeYouTitle',
  props: {
    title: {
      type: String,
      default: () => '',
    },
  },
  setup(props) {
    const infos = reactive({
      titleImg:
        'https://oss.innomatch.net/static-assets/keyou/screen/title_bottom_decoration.png',
    });
    return {
      ...toRefs(props),
      ...toRefs(infos),
    };
  },
});
</script>
<style lang="stylus" scoped>
// TODO
</style>
