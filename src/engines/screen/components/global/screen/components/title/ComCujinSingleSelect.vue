<script lang="ts">
import { ref, defineComponent, toRefs } from 'vue';

export default defineComponent({
  name: 'ComCujinSingleSelect',
  components: {},
  props: {
    selectedOpt: { type: Object, default: () => {} },
    tabs: { type: Array, default: () => [] },
  },
  emits: ['update:selectedOpt'],

  setup(props, { emit }) {
    const onTab = (item: any) => {
      emit('update:selectedOpt', item);
    };
    return {
      ...toRefs(props),
      onTab,
    };
  },
});
</script>

<template lang="pug">
.ComCujinSingleSelect
  a-dropdown(trigger="click")
    .time_btn.px-2.py-1.flex.items-center
      .text-white.text-xs {{ selectedOpt?.name }}
      img.w-3.h-3(src="https://oss.innomatch.net/static-assets/cujin/screen/down_btn_icon.png")
    template(#overlay)
      .div.h-30.overflow-y-scroll.w-92px
        .tab.w-full.text-center.p-3.cursor-pointer.relative.z-99999(
          @click="onTab(item)"
          :class="`text-white bg-[#114D5B] hover:bg-[#295F6B] hover:text-white`"
          v-for="item in tabs"
        )
          .text-xs.nowrap {{item?.name}}
</template>

<style lang="stylus" scoped>
.ComCujinSingleSelect
  .time_btn
    cursor pointer
    height 25px
    background: linear-gradient(90deg, rgba(0, 225, 255, 0) -2.65%, rgba(0, 225, 255, 0.5) 53.82%, rgba(0, 225, 255, 0) 103.09%);
</style>
