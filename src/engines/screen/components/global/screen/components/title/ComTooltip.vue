<script lang="ts">
import { defineComponent, toRefs } from 'vue';

export default defineComponent({
  name: 'ComTooltip',
  components: {},
  props: {
    title: { type: String, default: '' },
    style: { type: Object, default: ()=> {} }
  },
  setup(props) {
    return {
      ...toRefs(props),
    };
  },
});
</script>

<template lang="pug">
.com_tooltip
  a-tooltip(color="#164F61")
    template(#title)
      .text-xs {{ title }}
    img.ml-1.w-4.h-4(:style='style',src="https://oss.innomatch.net/static-assets/cujin/screen/tooltip_icon.png")
</template>

<style lang="stylus" scoped></style>
