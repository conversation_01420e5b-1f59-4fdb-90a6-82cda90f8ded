<template lang="pug">
.ComCujinSubTitle.w-full.relative(class="h-[38px]")
  .flex.justify-between.items-center
    .title.text-lg.font-medium.ml-4 {{ title }}
    slot(name='rightContent')
  img.absolute.bottom-0.h-5.w-full(:src='titleImg')
</template>
<script lang="ts">
import { ref, defineComponent, reactive, toRefs } from 'vue';

export default defineComponent({
  name: 'ComCujinSubTitle',
  props: {
    title: {
      type: String,
      default: () => '',
    },
  },
  setup(props) {
    const infos = reactive({
      titleImg:
        'https://oss.innomatch.net/static-assets/cujin/screen/cujin_sub_title_bg.png',
    });
    return {
      ...toRefs(props),
      ...toRefs(infos),
    };
  },
});
</script>
<style lang="stylus" scoped>
.ComCujinSubTitle
  .title
    -webkit-background-clip: text;
    background-image: linear-gradient(180deg, #FFFFFF 0%, #00E1FF 100%);
    background-clip: text;
    color: transparent;
</style>
