<template lang="pug">
.com_keyou_title.w-full.relative.h-7
  .flex.justify-between.items-center(class="h-[22px]")
    .flex.items-center
      img.w-2.h-2.ml-1(:src="subTitleImg", class="mr-[6px]")
      .text-white.text-base.font-medium {{ title }}
    slot(name='rightContent')
  img.absolute.bottom-0.h-1.w-full(:src='titleImg')
</template>
<script lang="ts">
import { ref, defineComponent, reactive, toRefs } from 'vue';
export default defineComponent({
  name: 'ComKeYouSubTitle',
  props: {
    title: {
      type: String,
      default: () => '',
    },
  },
  setup(props) {
    const infos = reactive({
      titleImg:
        'https://oss.innomatch.net/static-assets/keyou/screen/sub_title_bottom.png',
      subTitleImg:
        'https://oss.innomatch.net/static-assets/keyou/screen/sub_title_icon.png',
    });
    return {
      ...toRefs(props),
      ...toRefs(infos),
    };
  },
});
</script>
<style lang="stylus" scoped>
// TODO
</style>
