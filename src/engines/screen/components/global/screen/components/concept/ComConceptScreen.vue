<script lang="ts">
import { ref, defineComponent, toRefs, onMounted, nextTick } from 'vue';
import Starfield from '@/engines/screen/components/Starfield.vue';
import PraticleEffect from '@/engines/screen/components/PraticleEffect.vue';
import ComConceptCard from './ComConceptCard.vue';

import practicle_effect_logo from '@/engines/screen/assets/point/practicle_effect_logo.png';

export default defineComponent({
  name: 'ComConceptScreen',
  components: {
    Starfield,
    ComConceptCard,
    PraticleEffect,
  },
  props: {
    value: { type: String, default: 'copy' },
  },
  setup(props) {
    const msg = ref('');

    const start_screen = ref(false);

    const isRevealed = ref(false);

    function onAnimationEnd() {
      // 内容完全进场后隐藏蓝条
      isRevealed.value = true;
    }
    const startScreenAction = () => {
      if (!start_screen.value) {
        start_screen.value = true;
      }
    };
    return {
      ...toRefs(props),
      practicle_effect_logo,
      msg,
      isRevealed,
      onAnimationEnd,
      start_screen,
      startScreenAction,
    };
  },
});
</script>

<template lang="pug">
.com-concept-screen.w-full.h-full.relative
  .absolute.left-0.w-full.flex.items-center.justify-center.z-2(v-if='!start_screen',style='bottom: 6%')
    .w-152px.h-52px.cursor-pointer.rounded-2xl.flex.items-center.justify-center(@click='startScreenAction()',style='background-color: rgba(58, 70, 94, 0.5)')
      .text-white.opacity-80.text-xl 点击进入

  Starfield(
    backgroundColor='transparent', 
    backgroundType='image', 
    backgroundImage='https://oss.innomatch.net/static-assets/screen/concept/starfield_bg_2.png'
    :speed='1.3',
    :fov='1000',
    :particleCount='500',
    particleColor='rgba(255, 255, 255, 0.4)'
    :maxDepth='2200',
    :minDepth='0'
  )
  .absolute.w-full.h-full.flex.items-center.justify-center.left-0.top-0.z-0
    .div.w-full.h-full
      PraticleEffect.w-full.h-full(
        :imageUrl='practicle_effect_logo',
        backgroundColor='transparent', 
        :imageSize = 'start_screen ? 100 : 350',
        :particleSize='start_screen ? 0.9 : 1.2',
        :particleStep='start_screen ? 2 : 3',
        :openChangeAnimation='true',
        :deepRange='start_screen ? 2 : 40',
        :perspective='500'
      )
  template(v-if='start_screen')
    img.absolute.right-1204px.top-98px.h-884px.edge_content.z-4(
      style="transform: rotateY(180deg);",
      :class="{ 'hidden_edge': isRevealed }",
      src='https://oss.innomatch.net/static-assets/screen/concept/card_edge_bg.jpg'
    )
    img.absolute.left-1204px.top-98px.h-884px.edge_content.z-4(
      :class="{ 'hidden_edge': isRevealed }",
      src='https://oss.innomatch.net/static-assets/screen/concept/card_edge_bg.jpg'
    )
  template(v-if='start_screen')

    .absolute.left-box.outer_action
      .w-full.h-full.relative.left-box-inner(:class='{ "start_left_ani": start_screen }',@animationend='onAnimationEnd')

        ComConceptCard.incubator_box.w-678px.h-412px(
          icon='https://oss.innomatch.net/static-assets/screen/concept/concept_card_icon/1.png',
          title='孵化器'
        )
          template(#content)
            .pt-71px
              .grid.grid-cols-2.gap-3
                .box1.h-140px.w-full.relative.cover_effect
                  ComConceptSubTitle.absolute.left-0.top-0.z-2(value='高质量孵化器')
                  ComConceptSubTitle.absolute.right-0.bottom-0.w-77px.z-2(value='8个')
                  img.w-full.h-full(src='https://oss.innomatch.net/static-assets/screen/concept/content/%E9%AB%98%E8%B4%A8%E9%87%8F%E5%AD%B5%E5%8C%96%E5%99%A8.gif')
                .box2.h-140px.w-full.relative
                  ComConceptSubTitle.absolute.left-0.top-0(value='孵化专业领域')
                  img.w-full.h-full(src='https://oss.innomatch.net/static-assets/screen/concept/content/%E5%AD%B5%E5%8C%96%E4%B8%93%E4%B8%9A%E9%A2%86%E5%9F%9F.png')
                .box3.h-140px.w-full.relative.cover_effect
                  ComConceptSubTitle.absolute.left-0.top-0.z-2(value='孵化器分析')
                  .flex.items-center.h-103px.mt-9
                    img.w-142px.h-full(src='https://oss.innomatch.net/static-assets/screen/concept/content/box_bg_1.gif')
                    .w-full.h-full.flex.flex-col.pl-23px.gap-3.justify-center.z-2
                      .flex.items-center
                        .font-DINAlternate.text-white.text-xl.font-bold 56
                        .ml-6px.text-white.text-xs 孵化器总量
                      .flex.items-center
                        .flex.items-baseline
                          .font-DINAlternate.text-white.text-xl.font-bold 18.24
                          .ml-1.text-white.text-xs 亿元
                          .ml-2.text-white.text-xs 总收入

                .box4.h-140px.w-full.relative.cover_effect
                  ComConceptSubTitle.absolute.left-0.top-0.z-2(value='孵化企业分析')
                  img.w-full.h-full(src='https://oss.innomatch.net/static-assets/screen/concept/content/%E5%AD%B5%E5%8C%96%E4%BC%81%E4%B8%9A%E5%88%86%E6%9E%90.gif')

        ComConceptCard.incubator_box.w-678px.h-244px.mt-3.x(
          icon='https://oss.innomatch.net/static-assets/screen/concept/concept_card_icon/2.png',
          title='企业检索'
        )
          template(#content)
            .grid.grid-cols-3.w-full.h-217px
              .cover_effect.relative
                img.w-full.h-full(src='https://oss.innomatch.net/static-assets/screen/concept/content/%E5%85%A8%E9%83%A8%E4%BC%81%E4%B8%9A.gif')
                .absolute.left-0.top-0.z-2.flex.flex-col.items-center.justify-center.w-full.h-full
                  .title_text_heiti.text-2xl(class='text-[#c8eaff]') 全部企业
                  .title_text_heiti.text-xl.text-white 327万

              .cover_effect.relative
                img.w-full.h-full.img-fit(src='https://oss.innomatch.net/static-assets/screen/concept/content/%E8%B5%9B%E9%81%93%E4%BC%81%E4%B8%9A.gif')
                .absolute.left-0.top-0.z-2.flex.flex-col.items-center.justify-center.w-full.h-full
                  .title_text_heiti.text-2xl(class='text-[#c8eaff]') 赛道企业
                  .title_text_heiti.text-xl.text-white 2.19万
              .cover_effect.relative
                img.w-full.h-full(src='https://oss.innomatch.net/static-assets/screen/concept/content/%E7%A7%91%E6%8A%80%E4%BC%81%E4%B8%9A.gif')
                .absolute.left-0.top-0.z-2.flex.flex-col.items-center.justify-center.w-full.h-full
                  .title_text_heiti.text-2xl(class='text-[#c8eaff]') 科技企业
                  .title_text_heiti.text-xl.text-white 24.78万


        ComConceptCard.incubator_box.w-678px.h-294px.mt-3(
          icon='https://oss.innomatch.net/static-assets/screen/concept/concept_card_icon/3.png',
          title='孵化数据大屏'
        )
          template(#content)
            .cover_effect
              img.w-full.h-266px.img-fit(src='https://oss.innomatch.net/static-assets/screen/concept/content/%E5%AD%B5%E5%8C%96%E6%95%B0%E6%8D%AE%E5%A4%A7%E5%B1%8F.gif')

  
    .absolute.right-box.outer_action
      .w-full.h-full.relative.right-box-inner(:class='{ "start_right_ani": start_screen }',@animationend='onAnimationEnd')
        ComConceptCard.incubator_box.w-678px.h-244px(
          icon='https://oss.innomatch.net/static-assets/screen/concept/concept_card_icon/2.png',
          title='企业检索'
        )
          template(#content)
            .grid.grid-cols-3.w-full.h-217px
              .cover_effect.relative.mt-56px
                img.w-full.h-full(src='https://oss.innomatch.net/static-assets/screen/concept/content/dynamic_flow.gif')


              .cover_effect.relative
                img.w-full.h-full.img-fit(src='https://oss.innomatch.net/static-assets/screen/concept/content/%E5%8F%B3%E8%B5%9B%E9%81%93%E4%BC%81%E4%B8%9A.gif')
                .absolute.left-0.top-0.z-2.flex.flex-col.items-center.justify-center.w-full.h-full
                  .title_text_heiti.text-2xl(class='text-[#c8eaff]') 赛道企业
              .cover_effect.relative
                img.w-full.h-full.img-fit(src='https://oss.innomatch.net/static-assets/screen/concept/content/%E5%8F%B3%E7%A7%91%E6%8A%80%E4%BC%81%E4%B8%9A.gif')
                .absolute.left-0.top-0.z-2.flex.flex-col.items-center.justify-center.w-full.h-full
                  .title_text_heiti.text-2xl(class='text-[#c8eaff]') 科技企业

        ComConceptCard.incubator_box.w-678px.h-544px.mt-3(
          icon='https://oss.innomatch.net/static-assets/screen/concept/concept_card_icon/4.png',
          title='重点赛道'
        )
          template(#content)
            .pt-71px
              .grid.grid-cols-2.gap-3
                .box1.h-210px.w-full.relative.cover_effect
                  img.w-full.h-full(src='https://oss.innomatch.net/static-assets/screen/concept/content/%E7%94%9F%E7%89%A9%E5%8C%BB%E8%8D%AF.gif')
                  .absolute.left-0.top-0.z-2.flex.flex-col.items-center.justify-center.w-full.h-full
                    .title_text_heiti.text-2xl(class='text-[#c8eaff]') 生物医药
                .box1.h-210px.w-full.relative
                  img.w-full.h-full(src='https://oss.innomatch.net/static-assets/screen/concept/content/%E4%BA%BA%E5%B7%A5%E6%99%BA%E8%83%BD.png')
                  .absolute.left-0.top-0.z-2.flex.flex-col.items-center.justify-center.w-full.h-full
                    .title_text_heiti.text-2xl(class='text-[#c8eaff]') 人工智能

                .box1.h-210px.w-full.relative.cover_effect
                  img.w-full.h-full(src='https://oss.innomatch.net/static-assets/screen/concept/content/%E9%9B%86%E6%88%90%E7%94%B5%E8%B7%AF.gif')
                  .absolute.left-0.top-0.z-2.flex.flex-col.items-center.justify-center.w-full.h-full
                    .title_text_heiti.text-2xl(class='text-[#c8eaff]') 集成电路

                .box1.h-210px.w-full.relative.cover_effect
                  img.w-full.h-full(src='https://oss.innomatch.net/static-assets/screen/concept/content/%E5%A4%A7%E6%A8%A1%E5%9E%8B.gif')
                  .absolute.left-0.top-0.z-2.flex.flex-col.items-center.justify-center.w-full.h-full
                    .title_text_heiti.text-2xl(class='text-[#c8eaff]') 大模型
        ComConceptCard.incubator_box.w-678px.h-156px.mt-3(
          icon='https://oss.innomatch.net/static-assets/screen/concept/concept_card_icon/3.png',
          title='个人中心'
        )
          template(#content)
            .pt-56px.bg-user-center.bg-cover.bg-center.bg-no-repeat(style='background-image: url(https://oss.innomatch.net/static-assets/screen/concept/content/user_center_bg.png)')
              .flex.h-65px.items-center(class='text-[#C8EAFF]')
                img.w-55px.h-full.ml-50px(src='https://oss.innomatch.net/static-assets/screen/concept/content/%E4%B8%AA%E4%BA%BA%E4%B8%AD%E5%BF%83.gif')
                .ml-27px.flex.items-baseline
                  .text-sm.font-medium 已填报数据
                  .ml-2.text-2xl.font-bold.font-DINAlternate 197
                  .ml-2px.text-xs.font-bold.font-DINAlternate 个

                .ml-40px.flex.items-baseline
                  .text-sm.font-medium 孵化企业
                  .ml-2.text-2xl.font-bold.font-DINAlternate 34
                  .ml-2px.text-xs.font-bold.font-DINAlternate 个

                .ml-51px.flex.items-baseline
                  .text-sm.font-medium 品牌活动
                  .ml-2.text-2xl.font-bold.font-DINAlternate 125
                  .ml-2px.text-xs.font-bold.font-DINAlternate 个

</template>

<style lang="stylus" scoped>
.com-concept-screen
  perspective 100rem

  // 左侧展开动画
  @keyframes reveal-left
    0%
      transform: translateX(100%); /* 从右侧完全不可见区域 */
      opacity: 0;
    // 10%
    //   overflow hidden
    100%
      transform: translateX(0); /* 完全进入可见区域 */
      opacity: 1;

  @keyframes reveal-right
    0%
      transform: translateX(-100%); /* 从右侧完全不可见区域 */
      opacity: 0;
    100%
      transform: translateX(0); /* 完全进入可见区域 */
      opacity: 1;


  .left-box
    left 80px
    width 678px
    top 0
    height 100%
    padding 55px 0
    overflow hidden
    transform: rotateY(20deg) translate(0.25rem) scaleX(0.9);


  .left-box-inner
    position: relative;
    transform: translateX(0%);

  .start_left_ani
    animation reveal-left 1.5s ease-out forwards

  .right-box
    width 678px
    right 80px
    top 0
    padding 55px 0
    overflow hidden
    transform: rotateY(-20deg) translate(0.25rem) scaleX(0.9);

  .right-box-inner
    position: relative;
    transform: translateX(0%);

  .start_right_ani
    animation reveal-right 1.5s ease-out forwards


  .edge_content
    margin-top 0.5rem
    mix-blend-mode: plus-lighter;

  .outer_action
    perspective 100rem
    transition all 1s linear
    scale 0.95

  .hidden_edge {
    opacity: 0;
    pointer-events: none;
    transition: opacity 0.3s ease-out;
  }
  .font-DINAlternate
    font-family: DIN Alternate;
  .cover_effect
    &::before
      content: ""; /* 伪元素必须有内容 */
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background-color: rgba(31, 62, 116, 0.2); /* 半透明背景色 */
      z-index: 1; /* 确保它在图片上面 */
  .img-fit
    object-fit: cover;         /* 图片填充容器并保持比例 */
    object-position: center;   /* 图片居中显示 */
  .title_text_heiti
    font-family: YouSheBiaoTiHei;

  // .start_particle_effect
  //   animation  1.5s particle_effect ease-out forwards

  // @keyframes particle_effect
  //   0%
  //     width 900px
  //     height 900px

  //   // 10%
  //   //   overflow hidden
  //   100%
  //     width 600px
  //     height 600px
</style>
