<script lang="ts">
import { ref, defineComponent, toRefs } from 'vue';

const ComConceptSubTitle = defineComponent({
  name: 'ComConceptSubTitle',
  components: {},
  props: {
    value: { type: String, default: '这是小标题' },
  },
  setup(props) {
    const msg = ref('');
    return {
      ...toRefs(props),
      msg,
    };
  },
});
export default ComConceptSubTitle;
</script>

<template lang="pug">
.com_concept_sub_title.w-full.h-9.flex.items-center
  .ml-4.sub_title_heiti {{value}}
</template>

<style lang="stylus" scoped>
.com_concept_sub_title
  background-image url('https://oss.innomatch.net/static-assets/screen/concept/sub_title_bg.png')
  background-size: 100% 100%; /* 横向拉伸，纵向保持100% */
  .sub_title_heiti
    font-family: YouSheBiaoTiHei;
    font-size: 20px;
    font-weight: 400;
    line-height: 26px;
    text-align: left;
    text-underline-position: from-font;
    text-decoration-skip-ink: none;
    color #C8EAFF
</style>
