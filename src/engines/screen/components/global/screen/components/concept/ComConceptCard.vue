<script lang="ts">
import { ref, defineComponent, toRefs } from 'vue';

const ComConceptCard = defineComponent({
  name: 'ComConceptCard',
  components: {},
  props: {
    icon: {
      type: String,
      default:
        'https://oss.innomatch.net/static-assets/screen/concept/concept_card_icon/1.png',
    },
    title: { type: String, default: '这是标题' },
  },
  setup(props) {
    const msg = ref('');
    return {
      ...toRefs(props),
      msg,
    };
  },
});
export default ComConceptCard;
</script>

<template lang="pug">
.ComConceptCard.w-full.h-full.flex.flex-col.relative
  .top_concept_card_bg.h-64px
  .center_concept_card_bg.flex-grow.w-full
  .bottom_concept_card_bg.h-64px
  .absolute.left-0.top-0.content-box.h-full.w-full
    .m-4.relative
      .title_concept_card_bg.h-56px.w-full.absolute.left-0.top-0.flex.items-center.z-2
        img.ml-4.w-8-h-8(:src='icon')
        .ml-10px.title_heiti {{title}}

      
      slot(name='content')
        .w-full.h-full
  .lump_list_bottom
    .lump_item(v-for='index in 5')
  .lump_list_Y.left-0
    .lump_item_Y.lump_item_Y_left(v-for='index in 2')
  .lump_list_Y.right-0
    .lump_item_Y.lump_item_Y_right(v-for='index in 2')


</template>

<style lang="stylus" scoped>
.ComConceptCard
  .top_concept_card_bg
    background-image url('https://oss.innomatch.net/static-assets/screen/concept/top_concept_card_bg.png')
    background-size: 100% 100%; /* 横向拉伸，纵向保持100% */

  .center_concept_card_bg
    background-image url('https://oss.innomatch.net/static-assets/screen/concept/center_concept_card_bg.png')
    background-repeat: repeat-y; /* 图片仅纵向平铺 */
    background-size: 100% auto; /* 图片宽度铺满，高度自动拉伸 */
    background-position: center; /* 图片居中显示 */
  .bottom_concept_card_bg
    background-image url('https://oss.innomatch.net/static-assets/screen/concept/bottom_concept_card_bg.png')
    background-size: 100% 100%; /* 横向拉伸，纵向保持100% */

  .title_concept_card_bg
    background-image url('https://oss.innomatch.net/static-assets/screen/concept/title_concept_card_bg.png')
    background-size: 100% 100%; /* 横向拉伸，纵向保持100% */

  .title_heiti
    font-family: YouSheBiaoTiHei;
    font-size: 32px;
    font-weight: 400;
    line-height: 41.6px;
    text-align: left;
    text-underline-position: from-font;
    text-decoration-skip-ink: none;
    color #7addff

// ----------点移动效果样式
  .lump_list_bottom
    position: absolute
    width: 100%
    left: 0
    bottom: 0
  .lump_item
    position: absolute
    bottom 0
    height 4px
    width 12px
    background-color #32c4fd
    transform: skewX(-20deg); /* 水平倾斜 -20 度 */

    &:nth-child(1)
      left: 40px
      animation lump1-animation-move-first 30s linear infinite

    &:nth-child(2)
      left: 55px
      animation lump1-animation-move-second 30s linear infinite


    &:nth-child(3)
      left: 70px
      animation lump1-animation-move-third 30s linear infinite


    &:nth-child(4)
      left: 85px
      animation lump1-animation-move-fourth 30s linear infinite

    &:nth-child(5)
      left: 100px
      animation lump1-animation-move-fifth 30s linear infinite

  @keyframes lump1-animation-move-first
    0%
      left: 40px
    40%
      left: 40px
    50%
      left: calc(100% - 100px - 12px)
    60%
      left: 40px
    100%
      left: 40px

  @keyframes lump1-animation-move-second
    0%
      left: 55px
    30%
      left: 55px
    40%
      left: calc(100% - 85px - 12px)
    60%
      left: calc(100% - 85px - 12px)
    70%
      left: 55px
    100%
      left: 55px

  @keyframes lump1-animation-move-third
    0%
      left: 70px
    20%
      left: 70px
    30%
      left: calc(100% - 70px - 12px)
    70%
      left: calc(100% - 70px - 12px)
    80%
      left: 70px
    100%
      left: 70px


  @keyframes lump1-animation-move-fourth
    0%
      left: 85px
    10%
      left: 85px
    20%
      left: calc(100% - 55px - 12px)
    80%
      left: calc(100% - 55px - 12px)
    90%
      left: 85px
    100%
      left: 85px

  @keyframes lump1-animation-move-fifth
    0%
      left: 100px
    10%
      left: calc(100% - 40px - 12px)
    90%
      left: calc(100% - 40px - 12px)
    100%
      left: 100px

  .lump_list_Y
    position: absolute
    height: 100%
    top: 0

    .lump_item_Y_left
      transform: skewY(30deg);
      left 0.8px

    .lump_item_Y_right
      transform: skewY(-30deg);
      right 0.8px


    .lump_item_Y
      position: absolute
      height: 6.8px
      width: 2px
      background-color #32c4fd

      &:nth-child(1)
        top 64px
        animation lump_y_1 10s linear infinite

      &:nth-child(2)
        bottom 64px
        animation lump_y_2 10s linear infinite

  @keyframes lump_y_1
    0%
      top 64px
    50%
      top calc(100% - 72px)
    100%
      top 64px

  @keyframes lump_y_2
    0%
      bottom 64px
    50%
      bottom calc(100% - 72px)
    100%
      bottom 64px
</style>
