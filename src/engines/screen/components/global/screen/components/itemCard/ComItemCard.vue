<template lang="pug">
.com_item_card.w-full.h-full.bg-cover.bg-center.bg-no-repeat.flex.flex-col(
  :style="getItemStyle",
)
  .item_card_title.text-sm.font-normal(:style="{color: nameColor}") {{ name }}
  .item_card_content
    template(v-if='$slots.content')
      slot(name='content')
    .flex(v-else)
      .flex.items-baseline
        .text-xl.font-normal.text-bebas(:style="{color: valueColor}") {{ finalValue }}
        .text-sm.font-normal.opacity-70.text-white.ml-1 {{ valueDigit }}
      .text-xs.font-bold.ml-1.text-bebas(v-if="extra", :style="{color: extraColor}") {{ calExtra }}


</template>
<script lang="ts">
import { ref, defineComponent, reactive, toRefs, PropType, computed } from 'vue';
export default defineComponent({
  name: 'ComItemCard',
  props: {
    name: {
      type: String,
      default: () => 'test',
    },
    value: {
      type: Number,
      default: () => 123213,
    },
    valueDigit: {
      type: String,
      default: () => '',
    },
    valueThousandPoint: {
      type: Boolean,
      default: () => false,
    },
    nameColor: {
      type: String,
      default: () => '#AEC3DC',
    },
    valueColor: {
      type: String,
      default: () => '#FFFFFF',
    },
    padding: {
      type: String,
      default: () => '10px',
    },
    hasBg: {
      type: Boolean,
      default: () => false,
    },
    align: {
      type: String as PropType<'left' | 'center'>,
      default: () => 'center',
    },
    extra: {
      type: Number,
      default: () => 0,
    },
    extraColor: {
      type: String,
      default: () => '#FFFFFF',
    },
  },
  setup(props) {
    const infos = reactive({
      titleImg:
        'https://oss.innomatch.net/static-assets/keyou/screen/group_title_bottom_decoration.png',
    });
    const getItemStyle = computed(() => {
      return {
        padding: props.padding,
        'align-items': props.align,
        ...(props.hasBg
          ? {
              background:
                'linear-gradient(90deg, rgba(46, 84, 138, 0.8) 0%, rgba(46, 84, 138, 0) 102.36%)',
            }
          : {}),
      };
    });
    const finalValue = computed(() => {
      if (props.valueThousandPoint) {
        return props.value.toLocaleString();
      } else {
        return props.value;
      }
    });
    const calExtra = computed(() => {
      if (props.extra > 0) {
        return '+' + props.extra;
      } else if (props.extra < 0) {
        return '-' + props.extra;
      }
      return '';
    });
    return {
      ...toRefs(props),
      ...toRefs(infos),
      getItemStyle,
      finalValue,
      calExtra,
    };
  },
});
</script>
<style lang="stylus" scoped>
.text-bebas
  font-family: Bebas;
</style>
