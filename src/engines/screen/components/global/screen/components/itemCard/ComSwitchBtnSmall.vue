<template lang="pug">
.com_switch_btn_small.flex.h-6
  .btn_current.opacity-80.text-white.text-sm.text-light.flex.items-center.justify-center.bg-cover.bg-center.cursor-pointer(
    class="w-[60px]",
    style="background-image: url('https://oss.innomatch.net/static-assets/keyou/screen/btn_sm_unactive.png')"
    :class="{'active': active === '活动' }"
    @click="emitFunc('活动')"
  ) 活动
  .btn_all.opacity-80.text-white.text-sm.text-light.flex.items-center.justify-center.bg-cover.bg-center.cursor-pointer(
    class="w-[60px] ml-[-1px]",
    style="background-image: url('https://oss.innomatch.net/static-assets/keyou/screen/btn_sm_unactive.png')"
    :class="{'active': active === '资讯' }"
    @click="emitFunc('资讯')"
  ) 资讯


</template>
<script lang="ts">
import { defineComponent, toRefs } from 'vue';
export default defineComponent({
  name: 'ComSwitchBtnSmall',
  props: {
    active: {
      type: String,
      default: () => '活动',
    },
  },
  emits: ['update:active'],
  setup(props, { emit }) {
    const emitFunc = (value: string) => {
      emit('update:active', value);
    };
    return {
      ...toRefs(props),
      emitFunc,
    };
  },
});
</script>
<style lang="stylus" scoped>
.com_switch_btn_small
  .active
    background-image url('https://oss.innomatch.net/static-assets/keyou/screen/btn_sm_active.png') !important
    opacity 1
</style>
