<template lang="pug">
.com_switch_btn.flex.h-6
  .btn_current.opacity-80.text-white.text-sm.text-light.flex.items-center.justify-center.bg-cover.bg-center.cursor-pointer(
    class="w-[80px]",
    style="background-image: url('https://oss.innomatch.net/static-assets/keyou/screen/year_btn_unactive.png')"
    :class="{'active_year': activeYear === '2023' }"
    @click="emitFunc('2023')"
  ) 2023年
  .btn_all.opacity-80.text-white.text-sm.text-light.flex.items-center.justify-center.bg-cover.bg-center.cursor-pointer(
    class="w-[60px] ml-[-1px]",
    style="background-image: url('https://oss.innomatch.net/static-assets/keyou/screen/all_btn_unactive.png')"
    :class="{'active_all': activeYear === 'all' }"
    @click="emitFunc('all')"
  ) 累计


</template>
<script lang="ts">
import { defineComponent, toRefs } from 'vue';
export default defineComponent({
  name: 'ComSwitchBtn',
  props: {
    activeYear: {
      type: String,
      default: () => '2023',
    },
  },
  emits: ['update:activeYear'],
  setup(props, { emit }) {
    const emitFunc = (value: string) => {
      emit('update:activeYear', value);
    };
    return {
      ...toRefs(props),
      emitFunc,
    };
  },
});
</script>
<style lang="stylus" scoped>
.com_switch_btn
  .active_year
    background-image url('https://oss.innomatch.net/static-assets/keyou/screen/year_btn_active.png') !important
    opacity 1
  .active_all
    background-image url('https://oss.innomatch.net/static-assets/keyou/screen/all_btn_active.png') !important
    opacity 1
</style>
