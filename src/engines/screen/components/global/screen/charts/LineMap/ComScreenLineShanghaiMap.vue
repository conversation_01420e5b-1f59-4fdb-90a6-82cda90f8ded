<template lang="pug">
.w-full.h-full
  TaScreenEchartBase(:options='option' ref='echartsRef')
</template>
<script lang="ts">
import { getThemeColor } from '@/engines/screen/utils/util';
import { VObject } from '@/lib/vails';
import { PropType, computed, defineComponent, onMounted, ref, toRefs } from 'vue';
import {
  ForceGraphData,
  ForceGraphLineLevelData,
  ScreenLinePointMap,
  forceGraphAreaColor,
  forceGraphColor,
} from '../baseUtils/baseChat';

// 添加地图加载函数
const loadShanghaiMap = async () => {
  try {
    console.log('尝试加载上海地图数据...');
    // 尝试多种可能的路径
    const paths = [
      'echarts/map/js/province/shanghai',
      'echarts/map/json/province/shanghai',
      '@/assets/map/shanghai.json',
    ];

    for (const path of paths) {
      try {
        await import(path);
        console.log(`成功从 ${path} 加载上海地图数据`);
        return true;
      } catch (e) {
        console.debug(`路径 ${path} 未找到上海地图数据，这可能是正常的`);
      }
    }

    // 如果所有路径都加载失败，返回false但不报错
    console.info('未找到上海地图数据，将使用基础地图显示');
    return false;
  } catch (error) {
    console.info('地图加载失败，将使用基础地图显示:', error);
    return false;
  }
};

const ComScreenLineShanghaiMap = defineComponent({
  name: 'ComScreenLineShanghaiMap',
  components: {},
  props: {
    // 需要merge的option - 高级功能
    mergeOption: { type: Object, default: () => {} },
    // 是否完全替换
    isOnlyMerge: { type: Boolean, default: false },
    // 是否需要加载上海地图
    needShanghaiMap: { type: Boolean, default: true },

    // 自定义色彩，【或者可以支持组选新颜色?】
    customizeLineColor: { type: Array, default: () => [] },
    customizeAreaColor: { type: Array, default: () => [] },

    // 图表数据 seriesData 为纯数据项 , seriesName 对应的每一例数据代表的数据别称
    data: {
      type: Array as PropType<ForceGraphData[]>,
      default: () => [],
    },
    // 有几个点值
    pointMap: {
      type: Array as PropType<ScreenLinePointMap[]>,
      default: () => [],
    },
    // 图例各区间范围值
    lineLevel: {
      type: Array as PropType<ForceGraphLineLevelData[]>,
      default: () => [],
    },
  },
  setup(props: any) {
    const echartsRef = ref();
    const mapLoaded = ref(false);
    const mergeBaseOption = (options: VObject, onlyMerge: boolean) => {
      echartsRef.value?.mergeOption(options, onlyMerge);
    };

    const finalLineColor = [...props.customizeLineColor, ...forceGraphColor];
    const finalAreaColor = [...props.customizeAreaColor, ...forceGraphAreaColor];

    function convertLineData(e: any[]) {
      const n: any = [];
      const t: any = [];
      const r = 0.5;
      const o = 5;
      const h: any = [];
      e.forEach(function (e) {
        t.push(e.value);
      });
      for (let c = Math.max.apply(null, t), u = Math.min.apply(null, t), v = 0; v < t.length; v++)
        h.push(t[v] === 0 ? 0 : ((o - r) * (t[v] - u)) / (c - u) + r);
      for (let m = 0; m < e.length; m++) {
        const i = h.shift();
        const p = e[m];
        const s = props.pointMap.find(({ name }: ScreenLinePointMap) => name === p.fromName).value;
        const f = props.pointMap.find(({ name }: ScreenLinePointMap) => name === p.toName).value;
        const lineColorIndex = props.lineLevel.findIndex(
          ({ gt, lte }: VObject) => p.value >= gt && p.value <= lte,
        );
        s &&
          f &&
          n.push({
            fromName: p.fromName,
            toName: p.toName,
            value: p.value,
            coords: [s, f],
            lineStyle: {
              width: i,
              opacity: 0.9,
              color: finalAreaColor[lineColorIndex],
            },
          });
      }
      return n;
    }

    onMounted(async () => {
      if (props.needShanghaiMap) {
        mapLoaded.value = await loadShanghaiMap();
      }
    });

    const option: any = computed(() => {
      const baseOption = {
        geo: {
          map: props.needShanghaiMap ? '上海' : 'none',
          roam: false,
          zoom: 1.2,
          zIndex: 100,
          aspectScale: 0.9,
          center: [121.3, 31.24],
          layoutSize: '100%',
          itemStyle: {
            normal: {
              areaColor: '#234A77',
              borderColor: '#B9EDFE',
              borderWidth: 3,
            },
          },
        },
        visualMap: {
          show: !0,
          type: 'piecewise',
          pieces: props.lineLevel.map((item: VObject, index: number) => {
            return {
              ...item,
              color: finalLineColor[index],
              symbol:
                'path://M576.08,55.1,478.6,94.22a8.29,8.29,0,0,1-11.38-7.7V80.46a8.3,8.3,0,0,0-8-8.29L8,55.71a8.3,8.3,0,0,1-8-8.29H0a8.3,8.3,0,0,1,8-8.3L459.22,22.66a8.31,8.31,0,0,0,8-8.3v-6A8.3,8.3,0,0,1,478.6.6l97.48,39.1A8.29,8.29,0,0,1,576.08,55.1Z',
            };
          }),
          itemSymbol: 'arrow',
          left: -4,
          bottom: 15,
          itemWidth: 56,
          itemHeight: 14,
          textStyle: {
            color: getThemeColor(),
            fontSize: 12,
          },
          seriesIndex: 2,
          hoverLink: true,
        },
        series: [
          {
            type: 'map',
            map: '上海',
            name: 'w',
            roam: false,
            silent: true,
            zoom: 1.2,
            aspectScale: 0.9,
            center: [121.3, 31.24], // 调整地图位置
            showLegendSymbol: false, // 存在legend时显示
            label: {
              normal: {
                show: false,
              },
              emphasis: {
                show: false,
              },
            },
            selectedMode: 'single',
            itemStyle: {
              normal: {
                areaColor: '#234A77',
                borderColor: '#6580A0',
                borderWidth: 1,
              },
              emphasis: {
                areaColor: '#15C8CA',
                borderWidth: 1,
              },
            },
          },
          {
            name: 'scatter',
            type: 'scatter',
            coordinateSystem: 'geo',
            symbol:
              'image://' +
              'https://oss.innomatch.net/static-assets/screen/component/relation_line_map_point.png',
            symbolSize: [12, 16],
            symbolOffset: ['10%', '-30%'],
            silent: 1,
            label: {
              show: !0,
              position: 'bottom',
              formatter: function (e: any) {
                return e.data.name;
              },
              textStyle: {
                color: getThemeColor(),
                fontSize: 14,
                textShadowColor: '#032e6f',
                textShadowBlur: 10,
                fontFamily: '微软雅黑',
                backgroundColor: 'transparent',
              },
            },
            itemStyle: {
              color: '#eeec0d',
              opacity: 1,
            },
            data: props.pointMap,
            zlevel: 9,
          },
          {
            type: 'lines',
            coordinateSystem: 'geo',
            effect: {
              show: !1,
              symbolSize: 1,
              trailOpacity: 1,
              trailLength: 0,
            },
            lineStyle: {
              curveness: 0.2,
            },
            symbol: ['none', 'arrow'],
            symbolSize: 8,
            data: convertLineData(props.data[0]?.seriesData),
          },
        ],
      };

      // 如果不需要上海地图或加载失败，使用基础配置
      if (!props.needShanghaiMap || !mapLoaded.value) {
        baseOption.geo.map = 'none';
        // 可以在这里添加备用显示方案
      }

      return baseOption;
    });

    return {
      ...toRefs(props),
      option,
      echartsRef,
      mergeBaseOption,
      mapLoaded,
    };
  },
});
export default ComScreenLineShanghaiMap;
</script>
<style lang="stylus" scoped></style>
