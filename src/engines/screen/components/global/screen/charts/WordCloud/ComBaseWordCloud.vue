<script lang="ts">
import { computed, defineComponent, onMounted, ref, toRefs, watch, PropType } from 'vue';
import { wordCloudColor } from '../baseUtils/baseChat';
import * as echarts from 'echarts';
import 'echarts-wordcloud';

export default defineComponent({
  name: 'ComBaseWordCloud',
  props: {
    data: {
      type: Object,
      default: () => ({
        seriesName: 'wordCloud-1',
        seriesData: [
          {
            name: '通信',
            weight: '',
          },
          {
            name: '移动通信',
            weight: '',
          },
          {
            name: '通信协议及信号处理',
            weight: '',
          },
          {
            name: '计算机',
            weight: '',
          },
          {
            name: '网络接入及传输',
            weight: '',
          },
          {
            name: '有线电通信',
            weight: '',
          },
          {
            name: '互联网通信',
            weight: '',
          },
          {
            name: '电子',
            weight: '',
          },
          {
            name: '通信服务',
            weight: '',
          },
        ],
      }),
    },
    colorList: {
      type: Array,
      default: () => wordCloudColor,
    },
    isMaskImage: {
      type: Boolean,
      default: () => true,
    },
    sizeRange: {
      type: Array,
      default: () => [12, 24],
    },
    gridSize: {
      type: Number,
      default: () => 18,
    },
    width: {
      type: String,
      default: () => '100%',
    },
    height: {
      type: String,
      default: () => '100%',
    },
  },

  setup(props) {
    const chartRef: any = ref(null);
    const data = ref<any>([]);
    const maskImage = ref(new Image());
    let chart: any;

    const options = computed(() => {
      return {
        series: [
          {
            type: 'wordCloud',
            sizeRange: props.sizeRange,
            rotationRange: [0, 0],
            gridSize: props.gridSize,
            shape: 'pentagon',
            drawOutOfBound: false,
            width: props.width,
            height: props.height,
            ...(props.isMaskImage && {
              maskImage: maskImage.value,
            }),
            // left: 'center',
            // top: 'center',
            layoutAnimation: true,
            keepAspect: true,
            textStyle: {
              fontWeight: 400,
              color: 'red',
            },
            data: data.value,
          },
        ],
      };
    });

    onMounted(() => {
      chart = echarts.init(chartRef.value);
    });

    watch(
      () => props.data,
      () => {
        console.log('props.data', props.data);
        if (props.data) {
          data.value = props.data?.seriesData.map((item: any, index: number) => {
            return {
              name: item.name,
              value: item.weight || index,
              textStyle: {
                normal: { color: props.colorList[index % props.colorList?.length] },
                color: props.colorList[index % props.colorList?.length],
              },
            };
          });
          if (props.isMaskImage) {
            maskImage.value.onload = function () {
              options.value.series[0].maskImage;
              chart.setOption(options.value);
            };
            maskImage.value.crossOrigin = 'Anonymous';
            maskImage.value.src =
              'https://oss.innomatch.net/static-assets/shtic/mask.png';
          } else {
            chart.setOption(options.value);
          }
        }
      },
      { immediate: true },
    );

    return {
      ...toRefs(props),
      chartRef,
      options,
    };
  },
});
</script>

<template lang="pug">
.com_word_cloud_wrap.flex.justify-center(:style='{width,height}')
  .box.w-full.h-full.overflow-hidden(ref='chartRef', id="word-cloud")
</template>

<style lang="stylus" scoped></style>
