<template lang="pug">
.w-full.h-full
  TaScreenEchartBase(:options='gaugeOption' ref='echartsRef')
</template>

<script lang="tsx">
import { PropType, computed, defineComponent, ref } from 'vue';
import { progressScreenData } from '../baseUtils/baseChat';
import { VObject } from '@/lib/vails';

export default defineComponent({
  name: 'ComScreenGauge',
  props: {
    // 需要merge的option - 高级功能
    mergeOption: { type: Object, default: () => {} },
    // 是否完全替换
    isOnlyMerge: { type: Boolean, default: false },
    data: {
      type: Array as PropType<progressScreenData[]>,
      default: () => [
        {
          seriesName: 'progress-1',
          seriesData: 80,
        },
      ],
    },
    gaugeType: {
      type: String as PropType<'border' | 'onlyLabel' | 'labelWithPointer'>,
      default: () => 'labelWithPointer',
    },
    center: {
      type: Array,
      default: () => ['50%', '50%'],
    },
    color: {
      type: String,
      default: () => '#91c7ae',
    },
    showTitle: {
      type: Boolean,
      default: () => true,
    },
    title: {
      type: String,
      default: () => '',
    },
    lastWeekCompare: {
      type: Number,
      default: () => 0,
    },
    lastMonthCompare: {
      type: Number,
      default: () => 0,
    },
    colorBorderSet: {
      type: Array,
      default: () => [],
    },
  },
  setup(props) {
    const echartsRef = ref();
    const mergeBaseOption = (options: VObject, onlyMerge: boolean) => {
      echartsRef.value?.mergeOption(options, onlyMerge);
    };

    const gaugeOption = computed(() => {
      const currentValue = props.data[0].seriesData;
      const showLabel = props.colorBorderSet.map((ele: any) => ele[0] * 100);
      return {
        series: [
          {
            name: '业务指标',
            type: 'gauge',
            center: props.center,
            detail: {
              formatter: (params: any) => {
                const title = props.showTitle ? `\n{title|${props.title}}` : '';
                const lastWeekWord = props.lastWeekCompare
                  ? `\n{upTitle|较上周}{${props.lastWeekCompare > 0 ? 'up' : 'down'}| }{${
                      props.lastWeekCompare > 0 ? 'upText' : 'downText'
                    }| ${props.lastWeekCompare}}`
                  : '';
                const lastMonthWord = props.lastMonthCompare
                  ? `\n{upTitle|较上月}{${props.lastMonthCompare > 0 ? 'up' : 'down'}| }{${
                      props.lastMonthCompare > 0 ? 'upText' : 'downText'
                    }| ${Math.abs(props.lastMonthCompare)}}`
                  : '';
                if (props.gaugeType !== 'labelWithPointer') {
                  return `{name|${params}}{percent|%}` + title + lastWeekWord + lastMonthWord;
                }
              },
              rich: {
                name: {
                  fontSize: 48,
                  padding: [10, 0, 0, 0],
                },
                percent: {
                  fontSize: 20,
                  padding: [0, 0, 0, 2],
                },
                title: {
                  fontSize: 12,
                  padding: [8, 0, 0, 0],
                  color: 'white',
                },
                upTitle: {
                  fontSize: 12,
                  padding: [6, 4, 6, 0],
                  color: 'white',
                },
                upText: {
                  fontSize: 12,
                  padding: [6, 4, 6, 0],
                  color: 'green',
                  fontWeight: 400,
                },
                up: {
                  width: 12,
                  height: 12,
                  padding: [0, 0, -4, 0],
                  align: 'center',
                  backgroundColor: {
                    image:
                      'https://oss.innomatch.net/static-assets/screen/component/progress_detail_up.png',
                  },
                },
                downTitle: {
                  fontSize: 12,
                  padding: [6, 4, 6, 0],
                  color: 'white',
                },
                downText: {
                  fontSize: 12,
                  padding: [6, 4, 6, 0],
                  color: 'red',
                  fontWeight: 400,
                },
                down: {
                  width: 12,
                  height: 12,
                  padding: [0, 0, -4, 0],
                  align: 'center',
                  backgroundColor: {
                    image:
                      'https://oss.innomatch.net/static-assets/screen/component/progress_detail_down.png',
                  },
                },
              },
              borderRadius: 8,
              offsetCenter: [0, props.gaugeType === 'border' ? '-20%' : '0%'],
            },
            data: [{ value: currentValue }],
            radius: props.gaugeType === 'border' ? '78%' : '90%',
            axisLine: {
              lineStyle: {
                color: (() => {
                  if (props.gaugeType === 'labelWithPointer' && props.colorBorderSet.length) {
                    return props.colorBorderSet;
                  } else {
                    return [
                      [currentValue / 100, props.color],
                      ...(props.gaugeType !== 'border' ? [[1, '#192132']] : []),
                    ];
                  }
                })(),

                width: props.gaugeType === 'border' ? 20 : 12,
              },
            },
            itemStyle: {
              opacity: props.gaugeType === 'labelWithPointer',
            },
            axisTick: {
              show: false,
            },
            splitLine: {
              show: false,
            },
            axisLabel: {
              show: false,
            },
            pointer: {
              show: props.gaugeType === 'labelWithPointer',
              length: '60%',
            },
            startAngle: props.gaugeType === 'border' ? 190 : 225,
            endAngle: props.gaugeType === 'border' ? -10 : -45,
          },
          ...(props.gaugeType === 'border'
            ? [
                {
                  name: '外圈',
                  radius: '80%',
                  type: 'gauge',
                  center: props.center,
                  data: [{ value: 0 }],
                  axisLine: {
                    lineStyle: {
                      color: props.colorBorderSet.length
                        ? props.colorBorderSet
                        : [[1, props.color]],
                      width: '2',
                    },
                  },
                  pointer: {
                    show: false,
                  },
                  axisTick: {
                    show: false,
                  },
                  splitLine: {
                    show: false,
                  },
                  axisLabel: {
                    show: false,
                  },
                  detail: {
                    show: false,
                  },
                  startAngle: 190,
                  endAngle: -10,
                },
                {
                  name: 'label标签',
                  radius: '104%',
                  type: 'gauge',
                  splitNumber: 100,
                  center: props.center,
                  data: [{ value: 0 }],
                  axisLine: {
                    show: false,
                  },
                  pointer: {
                    show: false,
                  },
                  axisTick: {
                    show: false,
                  },
                  splitLine: {
                    show: false,
                  },
                  axisLabel: {
                    ...(props.colorBorderSet.length
                      ? {
                          show: true,
                          distance: 0,
                          color: 'white',
                          formatter: (value: number) => {
                            const showList = [0, 100, ...showLabel];
                            console.log('value', value);
                            if (showList.includes(value)) return value;
                          },
                        }
                      : { show: false }),
                  },
                  detail: {
                    show: false,
                  },
                  startAngle: 190,
                  endAngle: -10,
                },
              ]
            : [
                {
                  name: 'axisTick',
                  radius: '78%',
                  type: 'gauge',
                  splitNumber: 10,
                  center: props.center,
                  data: [{ value: 0 }],
                  axisLine: {
                    show: false,
                  },
                  pointer: {
                    show: false,
                  },
                  axisTick: {
                    show: true,
                    length: 3,
                    splitNumber: 2,
                  },
                  splitLine: {
                    show: true,
                    length: 6,
                    lineStyle: {
                      width: 1,
                    },
                  },
                  axisLabel: {
                    show: false,
                  },
                  detail: {
                    show: false,
                  },
                  startAngle: 225,
                  endAngle: -45,
                },
                {
                  name: 'label标签',
                  radius: '90%',
                  type: 'gauge',
                  splitNumber: 5,
                  center: props.center,
                  data: [{ value: 0 }],
                  axisLine: {
                    show: false,
                  },
                  pointer: {
                    show: false,
                  },
                  axisTick: {
                    show: false,
                  },
                  splitLine: {
                    show: false,
                  },
                  axisLabel: {
                    show: true,
                    distance: 0,
                    color: 'white',
                  },
                  detail: {
                    show: false,
                  },
                  startAngle: 225,
                  endAngle: -45,
                },
              ]),
        ],
      };
    });

    return {
      gaugeOption,
      echartsRef,
      mergeBaseOption,
    };
  },
});
</script>

<style lang="stylus" scoped></style>
