import { VObject } from '@/lib/vails';

export interface BaseModel {
  created_at?: string;
  updated_at?: string;
}

export interface ServeSubmodule extends BaseModel {
  id: number;
  app_id: number;
  model_flag?: string;
  model_payload?: VObject;
  model_payload_summary?: VObject;
  model_detail?: VObject;
  ancestry?: string;
  depth?: number;
  children_count?: number;
  manages?: VObject;
  manage_enable?: boolean;
  name: string;
  state?: string;
  cover_image?: VObject;
  position?: number;
  key?: string;
  layout?: VObject;
}

export interface ServeBanner extends BaseModel {
  id: number;
  app_id: number;
  submodule_id?: number;
  source_type?: string;
  source_id?: number;
  model_flag?: string;
  model_payload?: VObject;
  model_payload_summary?: VObject;
  model_detail?: VObject;
  effective_at?: string;
  invalid_at?: string;
  published_at?: string;
  is_published?: boolean;
  name: string;
  position?: number;
  cover_image?: VObject;
  conf?: VObject;
}

export interface ServeCatalog extends BaseModel {
  id: number;
  app_id: number;
  submodule_id?: number;
  name: string;
  state?: string;
  position?: number;
}

export interface ServeTag extends BaseModel {
  id: number;
  app_id: number;
  submodule_id?: number;
  name: string;
  color?: string;
  position?: number;
  option?: VObject;
  activity_count?: number;
}

export interface ServeActivity extends BaseModel {
  id: number;
  source_type?: string;
  source_id?: number;
  app_id: number;
  submodule_id?: number;
  creator_id?: number;
  target_type?: string;
  target_id?: number;
  name: string;
  create_instance_state?: string;
  create_instance_timestamp?: string;
  model_flag?: string;
  model_payload?: VObject;
  model_payload_summary?: VObject;
  model_detail?: VObject;
  type?: string;
  effective_at?: string;
  invalid_at?: string;
  hotted_at?: string;
  is_hotted?: boolean;
  views?: VObject;
  view_enable?: boolean;
  uses?: VObject;
  use_enable?: boolean;
  manages?: VObject;
  manage_enable?: boolean;
  state?: string;
  cover_image?: VObject;
  icon?: VObject;
  attachments?: VObject;
  position?: number;
  content?: VObject;
  address?: string;
  layout?: VObject;
  views_count?: number;
  ai_summary?: string;
  province?: string;
  city?: string;
  district?: string;
  origin_id?: number;
}

export interface ServeEntry extends BaseModel {
  id: number;
  source_info?: VObject;
}

export interface ServePermitAction extends BaseModel {
  id: number;
}

export interface ServeBind extends BaseModel {
  id: number;
}

export interface ServeVerifyCode extends BaseModel {
  id: number;
  member_code: string;
}

export interface ServeScan extends BaseModel {
  id: number;
  code: string;
}

export interface ServeRegister extends BaseModel {
  id: number;
}

export interface ServeApp extends BaseModel {
  id: number;
}
export interface ServeOrg extends BaseModel {
  id: number;
}

export interface ServeGroup extends BaseModel {
  id: number;
}

export interface ServeOrigin extends BaseModel {
  id: number;
}

export interface ServeRule extends BaseModel {
  id: number;
  app_id: number;
  creator_id?: number;
  type?: string;
  model_flag?: string;
  model_payload?: VObject;
  model_payload_summary?: VObject;
  model_detail?: VObject;
  effective_at?: string;
  invalid_at?: string;
  name: string;
  state: string;
  position?: number;
  options?: VObject;
  batch_no?: number;
  code?: string;
  latest_send_at?: string;
  catalog_id?: number;
  rule_conf?: VObject;
  rule_record_type?: string;
  message_type?: string;
  rule_group_id?: number;
  content?: VObject;
  description?: string;
}

export interface ServePack extends BaseModel {
  id: number;
  app_id: number;
  rule_id?: number;
  activity_id?: number;
  create_instance_state?: string;
  create_instance_timestamp?: string;
  type?: string;
  name: string;
  state: string;
  seq?: string;
  period?: number;
  operate_at?: string;
  send_at?: string;
  payload?: VObject;
  option?: VObject;
  position?: number;
  creator_id?: number;
  org_id?: number;
  rule_record_id?: number;
  source_type?: string;
  source_id?: number;
  flag?: string;
  message_type?: string;
  tanent_id?: number;
  rule_item_id?: number;
  content?: string;
}

export interface ServeMessage extends BaseModel {
  id: number;
  app_id: number;
  creator_id?: number;
  org_id?: number;
  pack_id?: number;
  rule_id?: number;
  seq?: string;
  name: string;
  state?: string;
  content?: string;
  option?: VObject;
  payload?: VObject;
  dingtalk_revoke_status?: {
    has_dingtalk_message: boolean;
    dingtalk_message_id?: number;
    is_revoked: boolean;
    revoke_time?: string;
    state?: string;
  };
}

export interface ServeBidProject extends BaseModel {
  id: number;
}

export interface ServeAiMessage extends BaseModel {
  id: number;
  app_id?: number;
  creator_id: number;
  org_id?: number;
  pack_id?: number;
  rule_id?: number;
  ref_ai_message_id?: number;
  name: string;
  state: 'draft' | 'published' | 'closed';
  content: string;
  option?: VObject;
  payload?: VObject;
}

export type ServeAiMessageTemplate = ServeAiMessage;

export type ServeAiMessageSquare = ServeAiMessage;

export interface ServeAiPack extends ServePack {
  creator_id: number;
  rule_id?: number;
  payload?: {
    ai_contents?: Array<{
      seq: string;
      content: string;
    }>;
    message?: {
      title?: string;
      markdown?: string;
      single_title?: string;
    };
  };
}
