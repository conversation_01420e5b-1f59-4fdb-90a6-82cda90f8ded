import { MyApi } from '@/apis/MyApi';
import { ServeMessage } from '@/engines/iest/serve-core/types/model';
import { VApiConfig } from '@/lib/vails/api';

export class ServeManageMessagesApi extends MyApi<ServeMessage> {
  constructor(config?: VApiConfig) {
    super({
      namespace: '/serve/manage',
      name: 'message',
      actions: [
        { name: 'lite_index', method: 'get', on: 'collection' },
        { name: 'revoke', method: 'post', on: 'member' },
      ],
      ...config,
    });
  }
}
