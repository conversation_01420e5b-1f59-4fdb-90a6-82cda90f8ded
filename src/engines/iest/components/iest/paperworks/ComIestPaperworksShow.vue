<script lang="ts">
import { defineComponent, onBeforeUnmount, onMounted, toRefs } from 'vue';
import ComIestPaperworkPreviewer from './ComIestPaperworkPreviewer.vue';

const ComIestPaperworksShow = defineComponent({
  name: 'ComIestPaperworksShow',
  components: {
    ComIestPaperworkPreviewer,
  },
  props: {
    store: { type: Object, required: true },
  },
  emits: [],
  setup(props, { emit }) {
    onMounted(() => {
      document.documentElement.classList.toggle('dark__modal', true);
    });

    onBeforeUnmount(() => {
      document.documentElement.classList.toggle('dark__modal', false);
    });
    return {
      ...toRefs(props),
      record: props.store.record,
    };
  },
});
export default ComIestPaperworksShow;
</script>

<template lang="pug">
.com-iest-manage-paperworks-show
  ComIestPaperworkPreviewer(:record='record')
</template>

<style lang="stylus" scoped>
.com-iest-manage-paperworks-show
  height 100%
</style>
