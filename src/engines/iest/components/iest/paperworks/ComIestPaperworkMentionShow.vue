<script lang="ts">
import { ref, defineComponent, toRefs, watch } from 'vue';
import ComIestPaperworkPreviewerRiskCard from './ComIestPaperworkPreviewerRiskCard.vue';
import ComIestPaperworkState from './ComIestPaperworkState.vue';
import { VStore } from '@/lib/vails';
import { IestManagePaperworksApi } from '../../../apis/iest/manage/paperworks.api';
import { useCable } from '@/engines/base/channels/useCable';
import ComIestPaperworksShow from './ComIestPaperworksShow.vue';

const ComIestPaperworkMentionShow = defineComponent({
  name: 'ComIestPaperworkMentionShow',
  components: {
    ComIestPaperworkState,
    ComIestPaperworkPreviewerRiskCard,
    ComIestPaperworksShow,
  },
  props: {
    record: { type: Object, default: () => ({}) },
  },
  setup(props) {
    const store = new VStore(new IestManagePaperworksApi());
    store.extra.cable_key = 'iest_paperworks';
    useCable(store, { callback: { afterUpdate: () => store.find(props.record.mentionable_id) } });

    watch(
      () => props.record.mentionable_id,
      () => {
        if (props.record.mentionable_id) {
          store.find(props.record.mentionable_id);
        }
      },
      {
        immediate: true,
      },
    );

    const visible = ref(false);

    return {
      ...toRefs(props),
      store,
      mentionable: store.record,
      visible,
    };
  },
});
export default ComIestPaperworkMentionShow;
</script>

<template lang="pug">
.com-iest-paperwork-mention-show.h-full.w-full.p-4
  .card.shadow.rounded-xl.w-full.p-4
    .font-medium {{ mentionable?.attachment?.files?.[0]?.fileName || record.payload?.attachment?.files?.[0]?.fileName || '审查任务' }}
    .flex.justify-end.items-center.mt-2(v-if='mentionable?.state')
      a-button(
        v-if='mentionable.state === "success"',
        type='primary',
        @click.stop='() => visible = true',
      )
        | 查看详情
      ComIestPaperworkState(v-else, :record='mentionable')

  ComIestPaperworkPreviewerRiskCard.w-full.mt-4(
    v-if='mentionable?.id && mentionable.paperwork_results?.length > 0',
    class='dark:bg-[#09183F]'
    v-for="item in mentionable.paperwork_results"
    :level='`一般风险`',
    :ruleName='item.name',
    :content='{ "原文": item.raw }',
  )

  .w-full.h-full.flex.justify-center.items-center(v-else)
    .text-gray-400
      | 暂无风险信息

  TaNoPaddingModal(
    v-model:visible='visible',
    v-if='mentionable?.id',
    width='1600px',
  )
    .h-85vh.overflow-y-auto
      ComIestPaperworksShow(:store='store')
</template>

<style lang="stylus" scoped></style>
