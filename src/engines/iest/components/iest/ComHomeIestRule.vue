<script lang='ts'>
import { ref, defineComponent, toRefs, computed, reactive } from 'vue';
import ComHomeCardWrapper from './ComHomeCardWrapper.vue';
import ComIestProgress from '../ComIestProgress.vue';
import '@/engines/iest/views/table.styl';
import ComColorfulLabel from '@/engines/iest/components/global/ComColorfulLabel.vue';
import { VObject, VStore } from '@/lib/vails';
import ComServeRuleGrpInfoCard from '@/engines/iest/components/serve/rule_groups/ComServeRuleGrpInfoCard.vue';
import ComServeRuleShowDrawer from '@/engines/iest/components/serve/rules/ComServeRuleShowDrawer.vue';
import { IestAppsApi } from '@/engines/iest/apis/iest/apps.api';
import ComInstanceDrawer from '@/engines/iest/components/global/ComInstanceDrawer.vue';
import ComHomeRuleMsgPie from './ComHomeRuleMsgPie.vue';
import ComServeRuleIndexDrawer from '@/engines/iest/components/serve/rules/ComServeRuleIndexDrawer.vue';

const ComHomeIestRule = defineComponent({
  name: 'ComHomeIestRule',
  components: {
    ComHomeCardWrapper,
    ComIestProgress,
    ComServeRuleGrpInfoCard,
    ComColorfulLabel,
    ComServeRuleShowDrawer,
    ComInstanceDrawer,
    ComHomeRuleMsgPie,
    ComServeRuleIndexDrawer,
  },
  props: {
    title: { type: String, default: '' },
    url: { type: String, default: '' },
    store: { type: Object, default: () => ({}) },
    catalogStore: { type: Object, default: () => ({}) },
  },
  setup(props) {
    const appStore = new VStore(new IestAppsApi({}));
    const drawerVisible = ref(false)
    const visible = ref(false)
    const indexDrawerVisible = ref(false)
    const params = ref({})
    const activeRecord = ref({})
    const onOpenDrawer = (record: VObject) => {
      activeRecord.value = record
      params.value = {
        q: {
          'flowable_of_Serve::Pack_type_rule_rule_group_id_eq': record.id,
          state_not_in: ['completed', 'terminated']
        }
      }
      visible.value = true
    }
    const onOpenDrawer2 = (record: VObject) => {
      activeRecord.value = record
      params.value = {
        q: {
          rule_rule_group_id_eq: record.id,
          state_in: ['finished']
        }
      }
      drawerVisible.value = true
    }
    const onOpenDrawer3 = (record: VObject) => {
      activeRecord.value = record
      params.value = {
        q: {
          rule_group_id_eq: record.id
        }
      }
      indexDrawerVisible.value = true
    }

    const config = computed(() => ({
      recordName: '规则组',
      store: props.catalogStore,
      mode: 'table',
      actions: [
        { key: 'update', enabled: false },
        { key: 'delete', enabled: false },
      ],
      params: {
        q: {
          s: ['position asc'],
        }
      },
      pagination: {
        hide: true,
      },
      template: 'serve_rule_group'
    }))

    const state = reactive({
      pack_pending: 0,
      pack_completed: 0,
      rule_total: 0,
      rule_group_total: 0,
      msg_read: 0,
      msg_unread: 0,
    })

    const onFetchStateData = () => {
      appStore.sendMemberAction({
        id: 1,
        action: 'ta_resource_statistic',
        config: {
          data: {
            stat_condition: {
              refs: [
                {
                  relations: ['serve_packs'],
                  item: {
                    key: 'pack',
                    caculator: {
                      type: 'caculation',
                      caculations: [
                        { name: 'processing', method: 'count', filter: { state_eq: "pending" } },
                        { name: 'completed', method: 'count', filter: { state_not_in: ["pending", "terminated"] } },
                      ]
                    },
                  }
                },
                {
                  relations: ['serve_rules'],
                  item: {
                    key: 'rule',
                    caculator: {
                      type: 'caculation',
                      caculations: [
                        { name: 'total', method: 'count' },
                      ]
                    },
                  }
                },
                {
                  relations: ['serve_rule_groups'],
                  item: {
                    key: 'rule_group',
                    caculator: {
                      type: 'caculation',
                      caculations: [
                        { name: 'total', method: 'count' },
                      ]
                    },
                  }
                },
                {
                  relations: ['serve_messages'],
                  item: {
                    key: 'msg',
                    caculator: {
                      type: 'caculation',
                      caculations: [
                        { name: 'read', method: 'count', filter: { is_read_true: 1, rule_id_null: 0 } },
                        { name: 'unread', method: 'count', filter: { is_read_false: 1, rule_id_null: 0 } },
                      ]
                    },
                  }
                }
              ]
            }
          }
        }
      }).then((res: any) => {
        state.pack_pending = res.data.pack?.processing || 0
        state.pack_completed = res.data.pack?.completed || 0
        state.rule_total = res.data.rule?.total || 0
        state.rule_group_total = res.data.rule_group?.total || 0
        state.msg_read = res.data.msg?.read || 0
        state.msg_unread = res.data.msg?.unread || 0
      })
    }


    const onIndex = (data: VObject) => {
      onFetchStateData()
    }

    const pieData = computed(() => [
      {
        name: '未读',
        value: state.msg_unread
      },
      {
        name: '已读',
        value: state.msg_read
      }
    ])
    return {
      ...toRefs(props),
      state,
      config,
      drawerVisible,
      onOpenDrawer,
      params,
      activeRecord,
      onIndex,
      onOpenDrawer2,
      visible,
      pieData,
      indexDrawerVisible,
      onOpenDrawer3,
    };
  },
});
export default ComHomeIestRule;
</script>

<template lang="pug">
ComHomeCardWrapper(:title='title',:url='url')
  .com-home-iest-rule.iest__table__skin.space-y-4
    .pt-4.grid.grid-cols-3.gap-2
      .p-4.rounded-lg.bg-primary-50
        .text-xs.text-primary-900.mb-4 规则数量统计
        .grid.grid-cols-2.gap-2
          .name__and__value
            .text-lg.font-medium.text-gray-900.mb-1 {{ state.rule_group_total }}
            .text-xs.text-gray-500 规则分类数量
          .name__and__value
            .text-lg.font-medium.text-gray-900.mb-1 {{ state.rule_total }}
            .text-xs.text-gray-500 规则总数
      .p-4.rounded-lg.bg-yellow-50
        .text-xs.text-primary-900.mb-4 批次状态统计
        .space-y-2
          .legend.flex.items-center.text-gray-500.text-xs.space-x-1
            .w-10px.h-10px.rounded-full.bg-teal-400
            span 已审核
            span {{ state.pack_completed }}
          .legend.flex.items-center.text-gray-500.text-xs.space-x-1
            .w-10px.h-10px.rounded-full.bg-orange-300
            span 待审核
            span {{ state.pack_pending }}
          ComIestProgress.h-3(
            :data='[{name: "已审核", num: state.pack_completed,color:"#16BDCA"},{name: "待审核", num: state.pack_pending,color:"#FDBA8C"}]'
          )
      .p-4.rounded-lg.bg-gray-50
        .text-xs.text-primary-900.mb-1 消息状态统计
        ComHomeRuleMsgPie.h-78px(:data='pieData')

    TaIndexView.ta-index-view-skin(
      :config='config',
      :showHeader='false',
      @onIndex='onIndex',
    )
      template(#bodyCell='{record,column,text}')
        template(v-if='column.dataIndex[1] === "processing_packs_count"')
          ComColorfulLabel.px-10px.py-2px.cursor-pointer(
            :label='text',
            color='blue',
            @click.stop='onOpenDrawer(record)'
          )
        template(v-else-if='column.dataIndex[1] === "finished_packs_count"')
          ComColorfulLabel.px-10px.py-2px.cursor-pointer(
            :label='text',
            color='blue',
            @click.stop='onOpenDrawer2(record)'
          )
        template(v-else-if='column.dataIndex[1] === "rules_count"')
          ComColorfulLabel.px-10px.py-2px.cursor-pointer(
            :label='text',
            color='blue',
            @click.stop='onOpenDrawer3(record)'
          )
        .text-sm(v-else-if='column.dataIndex[0] === "orgs"') {{record.orgs?.map((org)=>org.name)?.join(',')}}

    ComServeRuleIndexDrawer(
      v-model:visible='indexDrawerVisible',
      :title='`${activeRecord.name} | 规则列表`',
      :params='params',
      :store='store',
      width='1100'
    )
    ComServeRuleShowDrawer(
      v-model:visible='drawerVisible',
      :title='`${activeRecord.name} | 已发送批次`',
      width='1100',
      :params='params',
      :ruleStore='store'
    )
      template(#drawer-header)
        ComServeRuleGrpInfoCard.mb-4(:record='activeRecord')
    ComInstanceDrawer(
      v-model:visible='visible',
      :title='`${activeRecord.name} | 待审核`'
      :params='params'
      width='900'
    )
      template(#drawer-header)
        ComServeRuleGrpInfoCard(:record='activeRecord')
    //- ComInstanceDrawer(
    //-   v-model:visible='drawerVisible',
    //-   title='待审核',
    //-   :workflowId='workflowId',
    //-   :params='params'
    //-   width='900'
    //- )
    //-
</template>

<style lang="stylus" scoped>
.com-home-iest-rule
  .legend
    .circle
      width 10px
      height @width
      border-radius 50%
</style>
