<script lang="ts">
import { TaBreadcrumbInterface } from '@/components/global/ta-component/TaBreadcrumb.vue';
import { ServeManageMessagesApi } from '@/engines/iest/serve-core/apis/serve/manage/messages.api';
import { message, Modal } from 'ant-design-vue';
import { computed, defineComponent, PropType, ref, toRefs } from 'vue';

const ComServeMessagesShow = defineComponent({
  name: 'ComServeMessagesShow',
  components: {},
  props: {
    store: { type: Object, required: true },
    extendRoute: { type: String, default: '' },
    editable: { type: Boolean, default: true },
    breadcrumbs: { type: Array as PropType<TaBreadcrumbInterface[]>, default: () => [] },
  },
  emits: ['afterDelete', 'afterExtend'],
  setup(props, { emit }) {
    const revoking = ref(false);

    const tabs = [
      {
        key: 'info',
        label: '基本信息',
      },
      // {
      //   key: 'xxx',
      //   label: 'xxx',
      // },
    ];

    // 撤回消息功能
    const handleRevoke = async () => {
      const record = props.store.record.value;
      if (!record?.id) {
        message.error('消息ID不存在');
        return;
      }

      // 二次确认对话框
      Modal.confirm({
        title: '确认撤回消息',
        content: '确定要撤回这条消息吗？撤回后用户将无法查看该消息，此操作不可撤销。',
        okText: '确认撤回',
        cancelText: '取消',
        okType: 'danger',
        onOk: async () => {
          try {
            revoking.value = true;
            const api = new ServeManageMessagesApi();
            const response = await api.sendMemberAction(record.id, 'revoke');
            const result = response.data;

            if (result.success) {
              message.success(result.message || '撤回成功');
              // 刷新数据
              props.store.find(record.id);
            } else {
              message.error(result.message || '撤回失败');
            }
          } catch (error: any) {
            console.error('撤回消息失败:', error);
            message.error(error.response?.data?.message || error.message || '撤回失败，请稍后重试');
          } finally {
            revoking.value = false;
          }
        },
      });
    };

    // 自定义 actions
    const customActions = computed(() => [
      {
        name: 'revoke',
        label: '撤回消息',
        icon: 'UndoOutlined',
        type: 'danger',
        loading: revoking.value,
        action: handleRevoke,
      },
    ]);

    const afterDelete = () => {
      emit('afterDelete');
    };

    const afterExtend = () => {
      emit('afterExtend');
    };

    return {
      ...toRefs(props),
      tabs,
      record: props.store.record,
      customActions,
      revoking,
      afterDelete,
      afterExtend,
      handleRevoke,
    };
  },
});
export default ComServeMessagesShow;
</script>

<template lang="pug">
.com-serve-manage-messages-show
  TaShowLayout(
    :tabs='tabs',
    :title='record.name',
    :store='store',
    :extendRoute='extendRoute',
    :editable='editable',
    :breadcrumbs='breadcrumbs',
    :actions='customActions',
    template='serve_message',
    @afterDelete='afterDelete',
    @afterExtend='afterExtend'
  )
    //- template(#xxx_tab)
</template>

<style lang="stylus" scoped>
.com-serve-manage-messages-show
  height 100%
</style>
