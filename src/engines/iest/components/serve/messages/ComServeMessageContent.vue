<script lang="ts">
import TaAttachments from '@/components/global/ta-component/file/TaAttachments.vue';
import { ServeManageMessagesApi } from '@/engines/iest/serve-core/apis/serve/manage/messages.api';
import { AuthSessionApi } from '@/engines/login/apis/auth/session.api';
import { message } from 'ant-design-vue';
import { computed, defineComponent, ref } from 'vue';

const ComServeMessageContent = defineComponent({
  name: 'ComServeMessageContent',
  components: {
    TaAttachments,
  },
  props: {
    record: { type: Object, required: true },
    showTitle: { type: Boolean, default: true },
    darkMode: { type: Boolean, default: false },
  },
  emits: ['revoked'],
  setup(props, { emit }) {
    const revoking = ref(false);
    const info = computed(() => AuthSessionApi.currentUser());
    const isAdmin = computed(() => info.value?.roles_name.includes('serve_admin'));
    function getTextFromHTML(htmlString: string) {
      if (!htmlString) return '';
      try {
        const parser = new DOMParser();
        const doc = parser.parseFromString(htmlString, 'text/html');
        return doc.body.textContent || '';
      } catch (e) {
        return htmlString;
      }
    }

    // 兼容内容字段
    const payloadContent = computed(() => props.record?.payload?.content?.content || '');
    const customContent = computed(
      () => props.record?.pack?.payload?.use_custom || props.record?.content,
    );
    const activityContent = computed(() => props.record?.pack?.activity?.content?.content || []);
    const payloadImageUrl = computed(() => props.record?.payload?.image?.url || '');
    const activityAttachmentFiles = computed(
      () => props.record?.pack?.activity?.attachments?.files || [],
    );
    const attachmentFiles = computed(() => props.record?.attachments?.files || []);
    const videos = computed(() => {
      // 优先 pack.activity.content.content[0].videos
      return props.record?.pack?.activity?.content?.content?.[0]?.videos || [];
    });

    const msgContentAndActivityContent = (msg: string, activityContent: any) => {
      msg = msg || '';
      if (!activityContent) return msg;
      try {
        const htmlString = (activityContent || []).map((item: any) => item.body).join('');
        return msg + getTextFromHTML(htmlString);
      } catch (e) {
        return msg;
      }
    };

    // 撤回消息功能
    const handleRevoke = async () => {
      if (!props.record?.id) {
        message.error('消息ID不存在');
        return;
      }

      // 二次确认对话框
      const { Modal } = await import('ant-design-vue');
      Modal.confirm({
        title: '确认撤回消息',
        content: '确定要撤回这条消息吗？撤回后用户将无法查看该消息，此操作不可撤销。',
        okText: '确认撤回',
        cancelText: '取消',
        okType: 'danger',
        onOk: async () => {
          try {
            revoking.value = true;
            const api = new ServeManageMessagesApi();
            const response = await api.sendMemberAction(props.record.id, 'revoke');
            const result = response.data;

            if (result.success) {
              message.success(result.message || '撤回成功');
              emit('revoked', result);
            } else {
              message.error(result.message || '撤回失败');
            }
          } catch (error: any) {
            console.error('撤回消息失败:', error);
            message.error(error.response?.data?.message || error.message || '撤回失败，请稍后重试');
          } finally {
            revoking.value = false;
          }
        },
      });
    };

    return {
      props,
      revoking,
      msgContentAndActivityContent,
      payloadContent,
      customContent,
      activityContent,
      payloadImageUrl,
      activityAttachmentFiles,
      attachmentFiles,
      videos,
      handleRevoke,
      isAdmin,
    };
  },
});
export default ComServeMessageContent;
</script>

<template lang="pug">
.com-serve-message-content(:class='{ "dark-mode": darkMode }')
  .content-header.mb-4(v-if='showTitle')
    .flex.justify-between.items-center
      .header__title.mb-2 {{ record?.payload?.content?.title || record?.payload?.card?.title || record?.activity?.name || record.name || record?.body?.action_card?.title || '廉洁提醒' }}
      .action-buttons(v-if='isAdmin')
        a-button.revoke-btn(
          type='primary',
          danger,
          size='small',
          :loading='revoking',
          @click='handleRevoke'
        )
          TaIcon(type='UndoOutlined')
          span 撤回消息
  .content-body
    p.pt-2(v-if='payloadContent', style='text-indent: 2rem') {{ payloadContent }}
    p.pt-2(v-else-if='customContent', style='text-indent: 2rem') {{ msgContentAndActivityContent(record.content, activityContent) }}
    section.pt-2(v-else-if='activityContent && activityContent.length')
      TaContentField(
        :disabled='true',
        :value='activityContent',
        v-if='Array.isArray(activityContent)'
      )
      TaContentField(:disabled='true', :value='[{ body: activityContent }]', v-else)
    section.pt-4.flex.justify-center(v-if='payloadImageUrl')
      img(:src='payloadImageUrl')
    section.pt-4.flex.justify-center(
      v-if='activityAttachmentFiles && activityAttachmentFiles.length'
    )
      TaAttachments(:attachments='activityAttachmentFiles', :disabled='true')
    section.pt-4.flex.justify-center(v-if='attachmentFiles && attachmentFiles.length')
      TaAttachments(:attachments='attachmentFiles', :disabled='true')
    section.pt-4.flex.justify-center(v-if='videos && videos.length')
      .video-container.w-full.max-w-3xl.overflow-hidden
        video.w-full.mx-auto(
          v-if='videos[0]',
          controls,
          preload='metadata',
          :src='videos[0].url',
          :poster='videos[0].poster',
          controlsList='nodownload'
        )
        p.text-center.mt-2.text-xs.text-blue-500
          a(:href='videos[0].url', target='_blank') 视频无法播放？点击此处直接访问
</template>

<style lang="stylus" scoped>
.com-serve-message-content
  .content-header
    border-bottom 1px solid #E5E7EB
    padding-bottom 10px
    .header__title
      color #1A56DB
      font-family 'PingFang SC'
      font-size 20px
      font-weight 500
      line-height 150%
    .action-buttons
      .revoke-btn
        display flex
        align-items center
        gap 4px
  .debug-info
    background-color #f9f9f9
    font-family monospace
  .content-body
    p
      color #374151
      font-size 16px
      line-height 1.6
    section
      margin-top 16px
      img
        max-width 100%
        border-radius 4px
    .video-container
      background-color #000
      border-radius 4px
      overflow hidden
      video
        max-width 100%
        width 100%
        height auto
        display block
  &.dark-mode
    .content-header
      border-bottom-color #2D3748
      .header__title
        color #90CDF4
    .content-body
      p
        color #E2E8F0
</style>
