<script lang="ts">
import { defineComponent, computed, ref, toRefs, reactive } from 'vue';
import { VObject } from '@/lib/vails/model';
import ComServeActivitiesShow from './ComServeActivitiesShow.vue';
import { TaIndexViewTabInterface } from '../../../../../components/global/ta-component/TaIndexView/ta-index-view-core/types';
import ComIestIndexModeSwitch from '../../ComIestIndexModeSwitch.vue';
import '@/engines/iest/views/table.styl';
import ComServeRelativedTags from '../tags/ComServeRelativedTags.vue';
import ComServeActivitiesCard from './ComServeActivitiesCard.vue';
import dayjs from 'dayjs';
import ComLabel from '../../global/ComLabel.vue';
import ComIestProgress from '../../ComIestProgress.vue';

const ComServeActivitiesIndex = defineComponent({
  name: 'ComServeActivitiesIndex',
  components: {
    ComServeActivitiesShow,
    ComIestIndexModeSwitch,
    ComServeRelativedTags,
    ComServeActivitiesCard,
    ComLabel,
    ComIestProgress,
  },
  props: {
    store: { type: Object, required: true },
    params: { type: Object, default: () => ({ q: { s: ['id desc'] } }) },
    needStatistic: { type: Boolean, default: false },
  },
  setup(props, { emit }) {
    const indexMode = ref<'table' | 'list'>('table');
    const taindexview = ref<any>(null);
    const state = reactive({
      totalCount: props.store.totalCount,
      text: 0,
      image: 0,
      video: 0,
    });

    const statisticData = computed(() => [
      {
        name: '图文类',
        num: state.image || 0,
        color: '#FDF6B2',
        bg: 'rgba(142, 75, 16, 1)',
        icon: 'image',
      },
      {
        name: '视频类',
        num: state.video || 0,
        color: 'rgba(222, 247, 236, 1)',
        bg: 'rgba(4, 108, 78, 1)',
        icon: 'videocamera',
      },
      {
        name: '文本类',
        num: state.text || 0,
        color: 'rgba(225, 239, 254, 1)',
        bg: 'rgba(26, 86, 219, 1)',
        icon: 'file-word',
      },
    ]);

    const fetchStatisticData = () => {
      props.store
        .sendCollectionAction({
          action: 'list_index',
          config: {
            data: {
              collection_stat_condition: {
                items: [
                  {
                    key: 'type',
                    caculator: {
                      type: 'caculation',
                      caculations: [
                        {
                          name: 'image',
                          filter: { content_type_tags_name_eq: '图片' },
                          method: 'count',
                        },
                        {
                          name: 'video',
                          filter: { content_type_tags_name_eq: '视频' },
                          method: 'count',
                        },
                        {
                          name: 'text',
                          filter: { content_type_tags_name_eq: '文字' },
                          method: 'count',
                        },
                      ],
                    },
                  },
                ],
              },
            },
          },
        })
        .then((res: any) => {
          const data = res.data?.statistics?.type;
          if (data) {
            const { image, video, text } = data;
            state.image = image || 0;
            state.video = video || 0;
            state.text = text || 0;
          }
        });
    };

    const config = computed(() => ({
      recordName: '素材管理',
      store: props.store,
      template: 'serve_activity',
      detail: {
        mode: 'drawer',
        width: '1100px',
      },
      params: props.params,
      mode: indexMode.value,
      // editApi:
      // showCount: true,
      actions: [
        { key: 'create', enabled: true },
        { key: 'update', enabled: true },
        { key: 'delete', enabled: true },
        { key: 'import', enabled: true },
        { key: 'export', enabled: true },
      ],
      table: {
        scroll: { y: 'auto' },
      },
      list: {
        splitCount: 3,
        gap: 16,
        scroll: { y: 'auto' },
      },
      searcherAccordionUseDrawer: true,
      searcherComplicatedOptions: [
        {
          key: 'name',
          label: '素材标题',
          type: 'string',
        },
        {
          key: 'created_at',
          label: '创建时间',
          type: 'time',
        },
        // {
        //   key: 'tags_id',
        //   label: '标签',
        //   type: 'select',
        //   path: '/serve/manage/submodules/1/tags',
        //   multiple: true,
        // },
        {
          label: '标签',
          type: 'dynamicComponent',
          component: 'ComTagGroupField',
        },
      ],
      searcherSimpleOptions: [{ key: 'name', label: '素材名称', type: 'string' }],
    }));

    // const statistics = ref({
    //   key1: 0,
    //   key2: 0
    // })

    // const tabs = computed<TaIndexViewTabInterface[]>(() => [
    //   {
    //     key: 'key1',
    //     label: '标签1',
    //     num: statistics.value.key1,
    //     query: {},
    //   },
    //   {
    //     key: 'key2',
    //     label: '标签2',
    //     num: statistics.value.key2,
    //     query: {},
    //   },
    // ]);

    const onIndex = (data: VObject) => {
      // statistics.value = data.statistics
      if (props.needStatistic) {
        fetchStatisticData();
      }
    };

    const onShow = async (record: any) => {
      await taindexview.value.syncEditRecord(record);
      taindexview.value.editable = false;
      taindexview.value.visibleDrawer = true;
    };

    const AI_SRC = 'https://stiei-obs2.obs.cn-east-2.myhuaweicloud.com/iest/ai_summary.png';

    return {
      ...toRefs(props),
      config,
      taindexview,
      // tabs,
      onIndex,
      indexMode,
      onShow,
      dayjs,
      state,
      statisticData,
      AI_SRC,
    };
  },
});

export default ComServeActivitiesIndex;
</script>

<template lang="pug">
.com-serve-manage-activities-index.iest__table__skin.flex.flex-col
  .dashboard.rounded-lg.bg-white.p-4.flex.items-center.mb-4(v-if='needStatistic')
    .total__item.flex.items-center.px-5
      .bg-primary-100.rounded-lg.w-12.h-12.flex.items-center.justify-center.mr-3
        TaIcon.text-primary-700.w-6.h-6(type='flowbite-v2-solid/swatchbook')
      .name__and__value
        .text-2xl.leading-tight.font-semibold.text-gray-900.mb-1 {{ state.totalCount }}
        .text-sm.leading-tight.text-gray-500 素材总数
    .flex-grow.w-0
      .flex.items-center.w-full.justify-around.mb-4
        .item.flex.items-center(v-for='item in statisticData')
          .rounded-lg.w-8.h-8.flex.items-center.justify-center.mr-3(
            :style='`background-color: ${item.color}; color: ${item.bg};`'
          )
            TaIcon(:type='`flowbite-v2-solid/${item.icon}`', class='!w-4 !h-4')
          .name__and__value
            .text-2xl.leading-tight.font-semibold.text-gray-900.mb-1 {{ item.num }}
            .text-sm.leading-tight.text-gray-500 {{ item.name }}
      ComIestProgress.h-3(:data='statisticData')

  .px-4.pt-4.bg-white.flex-grow.h-0.rounded-lg
    TaIndexView.ta-index-view-skin(ref='taindexview', :config='config', @onIndex='onIndex')
      template(#card='{ record }')
        ComServeActivitiesCard.cursor-pointer(:record='record', @onShow='onShow(record)')
      template(#bodyCell='{ record, column, text }')
        ComServeRelativedTags(v-if='column.dataIndex[0] === "tag_ids"', :tags='record.tags')
        .flex.items-center(v-else-if='column.dataIndex[0] === "name"')
          a-tooltip.mr-1.flex-shrink-0(:title='record.ai_summary', v-if='record.ai_summary')
            .w-5.h-5
              img.w-full.h-full.rounded-full(:src='AI_SRC', alt='AI', width='20', height='20')
          span.text-sm {{ text }}
      template(#right-actions)
        ComIestIndexModeSwitch(v-model:indexMode='indexMode')
      template(#detail='{ record, onClose }')
        article.py-4.px-6
          header.mb-4
            .text-base.text-gray-900.mb-1 {{ record.name }}
            .flex.items-center
              .text-xs.text-gray-400.pr-4 {{ dayjs(record.created_at).format('YYYY-MM-DD HH:mm') }}
              .tags__wrapper.flex.flex-wrap.flex-grow.w-0.space-x-1
                ComLabel.px-10px.py-2px(
                  v-for='tag in record.tags',
                  :label='tag.name',
                  :bg='tag.color || "#1890ff"',
                  color='white'
                )
          section.pt-2(v-if='record.content')
            TaContentField(
              :disabled='true',
              :value='record.content.content',
              v-if='Array.isArray(record.content.content)'
            )
            TaContentField(:disabled='true', :value='[{ body: record.content.content }]', v-else)
          section.pt-4.flex.justify-center(v-if='record.attachments')
            TaAttachments(:attachments='record.attachments.files', :disabled='true')

    //- template(#card='{ record }')
    //- template(#table)
    //-    a-table-column(title='名称' dataIndex='record')
    //- template(#detail='{ record, onClose }')
    //-   ComServeActivitiesShow(
    //-     v-if='record.id'
    //-     :store='store'
    //-     :extendRoute='`/serve/manage/activities/${record.id}`'
    //-     :editable='editable'
    //-     @afterDelete='onClose'
    //-     @afterExtend='onClose'
    //-   )
</template>

<style lang="stylus" scoped>
.com-serve-manage-activities-index
  height 100%
  width 100%
  section
    :deep(img)
      margin auto
    :deep(video)
      margin auto
</style>
