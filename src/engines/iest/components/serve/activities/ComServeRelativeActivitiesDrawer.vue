<script lang='ts'>
import { defineComponent, toRefs, computed } from 'vue';
import ComServeActivitiesIndex from '../../serve/activities/ComServeActivitiesIndex.vue';
const ComServeRelativeActivitiesDrawer = defineComponent({
  name: 'ComServeRelativeActivitiesDrawer',
  components: {
    ComServeActivitiesIndex,
  },
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    activityStore: { type: Object, required: true },
    params: { type: Object, default: () => ({}) },
  },
  setup(props, { emit }) {
    const localVisible = computed({
      get() {
        return props.visible;
      },
      set(val) {
        emit('update:visible', val);
      },
    });
    return {
      ...toRefs(props),
      localVisible,
    };
  },
});
export default ComServeRelativeActivitiesDrawer;
</script>

<template lang="pug">
TaNoPaddingDrawer(v-if='localVisible', v-model:visible='localVisible', width='1100')
  .h-full
    ComServeActivitiesIndex(:params='params', :store='activityStore')
</template>

<style lang="stylus" scoped></style>
