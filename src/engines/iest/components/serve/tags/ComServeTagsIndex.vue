<script lang="ts">
import { defineComponent, computed, ref, Ref, toRefs, reactive } from 'vue';
import ComServeTagsShow from './ComServeTagsShow.vue';
import '@/engines/iest/views/table.styl';
import ComIestStatisticBlockCard from '../../ComIestStatisticBlockCard.vue';
import ComIestIndexModeSwitch from '../../ComIestIndexModeSwitch.vue';
import ComServeTagsCard from './ComServeTagsCard.vue';
import { VObject } from '../../../../../lib/vails/model/index';
import ComColorfulLabel from '../../global/ComColorfulLabel.vue';
import ComServeRelativeActivitiesDrawer from '../activities/ComServeRelativeActivitiesDrawer.vue';
import { cloneDeep } from 'lodash-es';

const ComServeTagsIndex = defineComponent({
  name: 'ComServeTagsIndex',
  components: {
    ComServeTagsShow,
    ComIestStatisticBlockCard,
    ComIestIndexModeSwitch,
    ComServeTagsCard,
    ComColorfulLabel,
    ComServeRelativeActivitiesDrawer,
  },
  props: {
    store: { type: Object, required: true },
    activityStore: { type: Object, required: true },
  },
  setup(props, { emit }) {
    const indexMode = ref<'table' | 'list'>('table');
    const statisticActivityStore = cloneDeep(props.activityStore);
    const state = reactive({
      totalCount: 0,
      relatived: 0,
    });

    const statisticData = computed(() => [
      {
        name: '标签总数',
        value: state.totalCount,
        color: 'yellow',
        icon: 'file-circle-plus',
      },
      {
        name: '已关联素材',
        value: state.relatived,
        color: 'green',
        icon: 'file-check',
      },
    ]);

    const visible = ref(false);
    const activeRecord = ref<any>({});
    const checkRelativeActivities = (record: VObject) => {
      activeRecord.value = record;
      visible.value = true;
    };

    const config = computed(() => ({
      recordName: '标签管理',
      store: props.store,
      template: 'serve_tag',
      detail: {
        mode: 'drawer',
        width: '900px',
      },
      mode: indexMode.value,
      actions: [
        { key: 'create', enabled: true },
        { key: 'update', enabled: true },
        { key: 'delete', enabled: true },
        { key: 'import', enabled: true },
        { key: 'export', enabled: true },
      ],
      table: {
        scroll: { y: 'auto' },
      },
      list: {
        splitCount: 4,
        gap: 16,
        scroll: { y: 'auto' },
      },
      searcherSimpleOptions: [{ key: 'name', label: '标签名称', type: 'string' }],
    }));

    // const statistics = ref({
    //   key1: 0,
    //   key2: 0
    // })

    // const tabs = computed<TaIndexViewTabInterface[]>(() => [
    //   {
    //     key: 'key1',
    //     label: '标签1',
    //     num: statistics.value.key1,
    //     query: {},
    //   },
    //   {
    //     key: 'key2',
    //     label: '标签2',
    //     num: statistics.value.key2,
    //     query: {},
    //   },
    // ]);

    const onIndex = (data: VObject) => {
      state.totalCount = data.total_count;
      statisticActivityStore
        .sendCollectionAction({
          action: 'list_index',
          config: {
            data: {
              collection_stat_condition: {
                items: [
                  {
                    key: 'tag',
                    caculator: {
                      type: 'caculation',
                      caculations: [
                        { name: 'activities_count', filter: { tags_id_gt: 0 }, method: 'count' },
                      ],
                    },
                  },
                ],
              },
            },
          },
        })
        .then((res: any) => {
          state.relatived = res.data?.statistics?.tag?.activities_count || 0;
        });
    };
    return {
      ...toRefs(props),
      config,
      statisticData,
      indexMode,
      onIndex,
      visible,
      activeRecord,
      checkRelativeActivities,
      // tabs,
    };
  },
});

export default ComServeTagsIndex;
</script>

<template lang="pug">
.com-serve-manage-tags-index.iest__table__skin.flex.flex-col
  ComServeRelativeActivitiesDrawer(
    v-model:visible='visible',
    :params='{ q: { tags_id_eq: activeRecord?.id, s: ["published_at desc"] } }',
    :activityStore='activityStore',
    :title='activeRecord.name'
  )
  .statistics.grid.grid-cols-4.gap-4.mb-4
    ComIestStatisticBlockCard(
      v-for='item in statisticData',
      :label='item.name',
      :value='item.value',
      :color='item.color',
      :icon='item.icon'
    )
  .px-4.pt-4.bg-white.flex-grow.h-0.rounded-lg
    TaIndexView.ta-index-view-skin(:config='config', @onIndex='onIndex')
      template(#bodyCell='{ record, column, text }')
        .rounded.w-5.h-5(
          v-if='column.dataIndex[0] === "color"',
          :style='`background-color:${text}`'
        )
        ComColorfulLabel.px-10px.py-2px.cursor-pointer(
          v-else-if='column.dataIndex[0] === "activities_count"',
          :label='text || 0',
          color='blue',
          @click.stop='checkRelativeActivities(record)'
        )
      template(#right-actions)
        ComIestIndexModeSwitch(v-model:indexMode='indexMode')
      template(#card='{ record, actions }')
        .tag__card__wrapper.relative
          ComServeTagsCard.cursor-pointer(:record='record')
          .edit.absolute.top-2.right-2.hidden.cursor-pointer(@click.stop='')
            TaIcon.w-5.h-5(type='outline/pencil', @click='actions.onEdit(record)')
            a-popconfirm(
              title='确认删除吗？',
              ok-text='确认',
              cancel-text='取消',
              @confirm='actions.onDelete(record)'
            )
              TaIcon.w-5.h-5(type='outline/trash')
    //- template(#card='{ record }')
    //- template(#table)
    //-    a-table-column(title='名称' dataIndex='record')
    //- template(#detail='{ record, onClose }')
    //-   ComServeTagsShow(
    //-     v-if='record.id'
    //-     :store='store'
    //-     :extendRoute='`/serve/manage/tags/${record.id}`'
    //-     :editable='editable'
    //-     @afterDelete='onClose'
    //-     @afterExtend='onClose'
    //-   )
</template>

<style lang="stylus" scoped>
.com-serve-manage-tags-index {
  height: 100%;
  width: 100%;

  .tag__card__wrapper:hover {
    .edit {
      display: flex;
    }
  }
}
</style>
