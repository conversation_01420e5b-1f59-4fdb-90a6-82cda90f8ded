FROM ruby-3.2.2:v1

# RUN sed -i 's/deb.debian.org/mirrors.aliyun.com/g' /etc/apt/sources.list.d/debian.sources \
# && apt-get clean \
# && apt-get update \
# && apt-get install -y nodejs wkhtmltopdf

# RUN apt-get update \
# && apt-get install -y nodejs wkhtmltopdf fonts-wqy-zenhei

ARG UID=1000
ARG GID=1000
ARG USER=deploy
ARG ROOT=home
ARG PROJECT=tallty

RUN groupadd -g ${GID} deploy && useradd --create-home -d /${ROOT}/${USER} --no-log-init -u ${UID} -g "${GID}" ${USER}
USER ${USER}

RUN bundle config mirror.https://rubygems.org https://gems.ruby-china.com/
# # 设置 RubyGems 镜像源为 Ruby China
RUN gem sources --add https://gems.ruby-china.com/ --remove https://rubygems.org/

RUN mkdir -p /${ROOT}/${USER}/.ssh
WORKDIR /${ROOT}/${USER}/${PROJECT}
