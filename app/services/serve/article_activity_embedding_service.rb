class Serve::ArticleActivityEmbeddingService
  include ActionView::Helpers::<PERSON><PERSON><PERSON><PERSON>elper

  def initialize(article)
    @article = article
  end

  def process
    p @article.id
    unless @article.ai_summary.present?
      process_ai_summary
    end
    unless @article.ai_summary_embedding.present?
      process_embedding
    end
  end

  def process_embedding
    if @article.ai_summary.present?
      embedding = ::Chat::OpenaiService.embeddings([@article.ai_summary]).first
      @article.update!(ai_summary_embedding: embedding)
    end
  end

  def process_ai_summary
    ai_summary = request_ai_summary
    if ai_summary["content"].present?
      content = ai_summary["content"].is_a?(Array) ? ai_summary["content"].join("\n") : ai_summary["content"]
      tag_names = ai_summary.each_with_object([]) do |(k, v), memo|
        if k.to_s.include?("_tags")
          memo << v
        end

        memo
      end.flatten

      new_tag_ids = @article.app.serve_tags.where(name: tag_names).pluck(:id)
      @article.tag_ids = new_tag_ids + @article.tag_ids
      @article.update!(
        ai_summary: "《#{@article.name}》#{content}",
        ai_tag_ids: new_tag_ids,
        model_payload: (@article.model_payload || {}).merge(
          district: ai_summary["district"],
        ),
      )
    else
      @article.update!(
        ai_summary: "《#{@article.name}》",
        ai_summary_embedding: nil,
        ai_tag_ids: [],
      )
    end
  end

  def request_ai_summary
    # prompt = "用户将提供文字与图片，可能是一篇廉政文章，如果有内容，请**概述罗列案例**与标签，内容中若有相关案例，请**指出相关事件缘由、人物对象、时间（节日）、方式**。若没有实际文章内容，返回空字符串。
    #   标签可选值为[\"#{@article.app.serve_tags.pluck(:name).join('","')}\"]。
    #   JSON 返回，概述 key 为 content。总结标签为 key 为 tags。提取地区 key 为 district。"

    prompt = <<~PROMPT
      **你是文章信息提取助手，你将提取文章的信息，包括标题、内容、图片等，JSON 字段返回**
      1. 总结案例概述(json 关键字为 content)，100字左右。
      2. 总结文章匹配的标签，在可选值中选择，没有符合的返回空数组，
        #{@article.app.serve_groups.where.not(name: "类型").each_with_index.map do |group, index|
      "2.#{index + 1}. ###{group.name}##(json 关键字为 #{group.name}_tags)，可选值:[\"#{group.tags.unscope(:order).pluck(:name).join('","')}\"]"
    end.join("\n")}
    PROMPT

    response = ::Chat::OpenaiService.completions({
      model: "gpt-4o-mini",
      messages: [
        { role: "system", content: prompt },
        {
          role: "user", content: content_schema,
        },
      ],
      response_format: { type: "json_object" },
    })

    JSON.parse(response)
  end

  def content_schema
    result = [
      { type: "text", text: "标题：《#{@article.name}》" },
    ]

    @article.content&.dig("content")&.map do |c|
      if c["body"].present? && (content = strip_tags(c["body"]).gsub("&nbsp;", "").strip) && content.present?
        result << { type: "text", text: content }
      end

      if c["body"].present? && (doc = Nokogiri::HTML(c["body"])) && (urls = doc.css("img[src]"))
        urls.each do |url|
          u = url.attributes["src"].value
          URI.open(u) rescue next
          url_parsed = parseUrl(u)
          result << { type: "image_url", image_url: { url: url_parsed } } if url_parsed
        end
      end
    end

    p result
  end

  MAX_WIDTH = 300
  MAX_HEIGHT = 1500

  def parseUrl(url)
    image = MiniMagick::Image.open(url)
    return nil if image.type == "GIF"

    if image.width > MAX_WIDTH || image.height > MAX_HEIGHT
      "#{url}?width=#{MAX_WIDTH}&height=#{MAX_HEIGHT}"
    else
      ratio = image.height.to_f / MAX_HEIGHT / 100
      "#{url}?width=#{ratio * image.width}&height=#{ratio * image.height}"
    end
  rescue
    nil
    # File.join(ENV['API_URL'], "ai/user/img?url=#{URI.encode_www_form_component(url)}")

    # image = MiniMagick::Image.open(url)
    # if image.width > MAX_WIDTH || image.height > MAX_HEIGHT
    #   image.resize("#{MAX_WIDTH}x#{MAX_HEIGHT}>")
    # else
    #   ratio = image.height.to_f / MAX_HEIGHT
    #   image.resize("#{ratio * image.width}x#{ratio * image.height}>")
    # end

    # image.quality(75)

    # to_base64(image)
  end

  # def to_base64(image)
  #   temp_file = Tempfile.new(['processed', '.jpg'])
  #   image.write(temp_file.path)
  #   base64_image = Base64.strict_encode64(File.read(temp_file.path))
  #   temp_file.close
  #   temp_file.unlink
  #   "data:image/jpeg;base64,#{base64_image}"
  # end

  def self.process_content_type_tag
    include ActionView::Helpers::SanitizeHelper

    img_tag_id = Serve::ContentTypeTag.find_by(name: "图片").id
    video_tag_id = Serve::ContentTypeTag.find_by(name: "视频").id
    text_tag_id = Serve::ContentTypeTag.find_by(name: "文字").id
    img_2_tag_id = Serve::ContentTypeTag.find_by(name: "漫画").id
    serve_video_tag = Serve::Tag.find_by name: "视频"

    Serve::Activity.find_each do |activity|
      p activity.id
      tag_ids = []
      activity.content&.dig("content")&.each do |c|
        if c["body"].present?
          content = strip_tags(c["body"]).gsub("&nbsp;", "").strip
          tag_ids << text_tag_id if content.present?
        end

        if c["body"].is_a?(String) && (doc = Nokogiri::HTML(c["body"])) && (urls = doc.css("img[src]")) && urls.present?
          tag_ids << img_tag_id
        end

        if c["body"].is_a?(String) && (doc = Nokogiri::HTML(c["body"])) && (urls = doc.css("video[src]")) && urls.present?
          tag_ids << video_tag_id
          activity.tag_ids = (activity.tag_ids | [serve_video_tag.id])
        end

        if c["videos"].is_a?(Array) && c["videos"].present?
          tag_ids << video_tag_id
          activity.tag_ids = (activity.tag_ids | [serve_video_tag.id])
        end
      end

      activity.ai_summary&.include?("漫画") && tag_ids << img_2_tag_id

      activity.update!(content_type_tag_ids: tag_ids.uniq)
    end
  end

  def self.ids
    include ActionView::Helpers::SanitizeHelper
    ids = []
    Serve::Activity.find_each do |x|
      has_content_text = x.content&.dig("content")&.map do |c|
        c["body"].present? && strip_tags(c["body"]).strip.present?
      end&.any?(&:itself)

      has_content_img = x.content&.dig("content")&.map do |c|
        c["body"].is_a?(String) && (doc = Nokogiri::HTML(c["body"])) && (urls = doc.css("img[src]")) && urls.present?
      end&.any?(&:itself)

      ids << x.id if has_content_text || has_content_img
    end
    ids
  end
end

# include ActionView::Helpers::SanitizeHelper
# i_ids = []
# Serve::Activity.where(id: img_ids).find_each do |x|
#   has_content_text = x.content&.dig('content')&.map do |c|
#     c['body'].present? && strip_tags(c['body']).strip.present?
#   end&.any?(&:itself)

#   has_content_img = x.content&.dig('content')&.map do |c|
#     c['body'].is_a?(String) && (doc = Nokogiri::HTML(c['body'])) && (urls = doc.css('img[src]')) && urls.reject {|x|x.include?('jpeg')}.present?
#   end&.any?(&:itself)

#   i_ids << x.id if !has_content_text || has_content_img
# end

# include ActionView::Helpers::SanitizeHelper
# k = Serve::ArticleActivityEmbeddingService
# ids = k.ids
# err = []
# Serve::Activity.where(id: ids).where('id > 10377').find_each do |x|
#   k.new(x).process
# rescue
#   err << x.id
# end
