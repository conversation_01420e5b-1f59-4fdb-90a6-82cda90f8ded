json.partial! "serve/messages/single", message: message
json.extract!(
  message,
  *message.class.try(:extra_view_attributes, "simple"),
  :org_name,
  :rule_name
)

json.user message.user, partial: "users/single", as: :user
json.user_duty_names message.user&.dingtalk_member&.pos_job
json.user_department_names message.user.try(:department_names)
json.sender message.sender, partial: "users/single", as: :user
json.pack message.pack, partial: "serve/packs/simple", as: :pack

# 添加钉钉消息撤回状态信息
json.dingtalk_revoke_status do
  dingtalk_message = message.dingtalk_messages.first

  if dingtalk_message
    json.has_dingtalk_message true
    json.dingtalk_message_id dingtalk_message.id
    json.is_revoked dingtalk_message.response&.dig("state") == "已撤回"
    json.revoke_time dingtalk_message.response&.dig("撤回时间") if dingtalk_message.response&.dig("state") == "已撤回"
    json.state dingtalk_message.response&.dig("state")
  else
    json.has_dingtalk_message false
    json.is_revoked false
  end
end

json.ta_statistics message.ta_statistic(@resource_stat_condition) if @resource_stat_condition.present?
