json.partial! "serve/messages/single", message: message
json.extract!(
  message,
  *message.class.try(:extra_view_attributes, "simple"),
  :org_name,
  :rule_name
)

json.user message.user, partial: "users/single", as: :user
json.user_duty_names message.user&.dingtalk_member&.pos_job
json.user_department_names message.user.try(:department_names)
json.sender message.sender, partial: "users/single", as: :user
json.pack message.pack, partial: "serve/packs/simple", as: :pack

# 添加钉钉消息撤回状态信息
json.dingtalk_revoke_status do
  if message.dingtalk_messages.any?
    # 检查是否有任何钉钉消息被撤回
    revoked_messages = message.dingtalk_messages.select { |dm| dm.response&.dig("state") == "已撤回" }

    json.has_dingtalk_messages true
    json.total_dingtalk_messages message.dingtalk_messages.count
    json.revoked_count revoked_messages.count
    json.is_revoked revoked_messages.any?
    json.is_fully_revoked revoked_messages.count == message.dingtalk_messages.count

    if revoked_messages.any?
      json.revoke_details revoked_messages.map do |dm|
        {
          dingtalk_message_id: dm.id,
          revoke_time: dm.response&.dig("撤回时间"),
          state: dm.response&.dig("state")
        }
      end
      # 获取最早的撤回时间
      json.first_revoke_time revoked_messages.filter_map { |dm| dm.response&.dig("撤回时间") }.min
      # 获取最晚的撤回时间
      json.last_revoke_time revoked_messages.filter_map { |dm| dm.response&.dig("撤回时间") }.max
    end
  else
    json.has_dingtalk_messages false
    json.total_dingtalk_messages 0
    json.revoked_count 0
    json.is_revoked false
    json.is_fully_revoked false
  end
end

json.ta_statistics message.ta_statistic(@resource_stat_condition) if @resource_stat_condition.present?
