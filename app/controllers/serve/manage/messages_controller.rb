class Serve::Manage::MessagesController < SimpleController::BaseController
  defaults(
    resource_class: Serve::Message,
    collection_name: 'messages',
    instance_name: 'message',
    view_path: 'serve/messages',
  )

  auth_action :user
  permit_action :serve_admin, :serve_manage

  # 轻量化列表API，只返回前端需要的字段
  def lite_index
    # 记录性能日志
    start_time = Time.now
    query_chain = end_of_association_chain
    # 处理distinct_off参数，避免DISTINCT和ORDER BY冲突
    params[:distinct_off] = true
    if params[:q].present?
      query_chain = ransack_association(query_chain, params[:q])
    end

    # 处理分页
    page = params[:page] || 1
    per_page = params[:per_page] || 20

    # 包含关联并分页
    messages = query_chain
      .includes(
        { user: {} },
        { sender: {} },
        { pack: { source: {} } }
      )
      .paginate(page: page, per_page: per_page)

    # 准备要返回的精简数据，只返回需要的字段
    lite_messages = messages.map do |message|
      {
        id: message.id,
        is_read: message.is_read,
        read_at: message.read_at,
        rule_name: message.rule_name,
        org_name: message.org_name,
        created_at: message.created_at,
        user: message.user ? { id: message.user.id, name: message.user.name } : nil,
        sender: message.sender ? { id: message.sender.id, name: message.sender.name } : nil,
        pack: message.pack ? {
          id: message.pack.id,
          source: message.pack.source ? { name: message.pack.source.name } : nil
        } : nil
      }
    end

    # 计时并记录日志
    end_time = Time.now
    duration = (end_time - start_time) * 1000.0 # 转换为毫秒

    Rails.logger.info "【消息轻量化API】处理时间: #{duration.round(2)}ms | 记录数: #{messages.size}"

    # 直接渲染JSON响应
    render json: {
      current_page: messages.current_page,
      total_pages: messages.total_pages,
      total_count: messages.total_entries,
      records: lite_messages
    }
  end

  # 撤回钉钉消息接口
  def revoke
    message = resource

    # 检查消息是否存在
    unless message
      render json: { success: false, message: '消息不存在' }, status: :not_found
      return
    end

    # 检查是否有关联的钉钉消息
    unless message.dingtalk_messages.any?
      render json: { success: false, message: '该消息没有关联的钉钉消息' }, status: :unprocessable_entity
      return
    end

    # 记录撤回结果
    revoke_results = []
    success_count = 0
    error_count = 0

    # 遍历所有关联的钉钉消息并调用撤回方法
    message.dingtalk_messages.each do |dingtalk_message|
      begin
        Rails.logger.info "开始撤回钉钉消息: ID=#{dingtalk_message.id}, Message ID=#{message.id}"

        # 调用钉钉消息的撤回方法
        result = dingtalk_message.revoke

        revoke_results << {
          dingtalk_message_id: dingtalk_message.id,
          success: true,
          result: result
        }
        success_count += 1

        Rails.logger.info "成功撤回钉钉消息: ID=#{dingtalk_message.id}"
      rescue => e
        Rails.logger.error "撤回钉钉消息失败: ID=#{dingtalk_message.id}, 错误: #{e.message}"

        revoke_results << {
          dingtalk_message_id: dingtalk_message.id,
          success: false,
          error: e.message
        }
        error_count += 1
      end
    end

    # 返回撤回结果
    render json: {
      success: error_count == 0,
      message: "撤回完成: 成功 #{success_count} 条，失败 #{error_count} 条",
      data: {
        message_id: message.id,
        total_dingtalk_messages: message.dingtalk_messages.count,
        success_count: success_count,
        error_count: error_count,
        results: revoke_results
      }
    }
  end

  protected

  def begin_of_association_chain
    current_user.has_role?(:serve_admin) || params[:action] == 'create' ? current_app : current_user
  end

  def after_association_chain(association)
    association.current_tanent
  end

  def method_for_association_chain
    :serve_messages
  end

  private

  def message_params
    params.require(:message).permit(
      *resource_class.try(:extra_permitted_attributes),
      :notifyable_type,
      :notifyable_id,
      :user_id,
      :seq,
      :type,
      :read_at,
      :is_read,
      :name,
      :state,
      :send_at,
      :content,
      :sender_id,
      :pack_id,
      body: {},
      response: {},
      payload: {},
    )
  end
end
