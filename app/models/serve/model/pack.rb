module Serve::Model::Pack
  extend ActiveSupport::Concern

  included do
    self.track_migration = true

    broadcasts

    include Bpm::Ext::Flowable
    acts_as_flowable_resource
    acts_as_list scope: [:app_id]

    stiable

    # association
    belongs_to :app
    belongs_to :tanent,   optional: true, class_name: '::Tanent'
    belongs_to :rule,     optional: true
    belongs_to :activity, optional: true
    belongs_to :creator,  optional: true, class_name: '::User'
    belongs_to :org,      optional: true, class_name: '::Org'
    belongs_to :rule_record, optional: true
    belongs_to :source,  polymorphic: true,  optional: true
    belongs_to :rule_item,   optional: true

    has_many :messages, dependent: :nullify
    has_many :ai_messages, dependent: :nullify
    has_many :ai_message_squares,   dependent: :nullify
    has_many :ai_message_templates, dependent: :nullify
    has_many :receivers, dependent: :destroy
    has_many :send_users, through: :receivers, source: :user

    # attribute
    attribute :name,       :string,   comment: '名称'
    attribute :state,      :string,   comment: '状态'
    attribute :seq,        :string,   comment: '标识'
    attribute :flag,       :string,   comment: '标识'
    attribute :period,     :integer,  comment: '周期'
    attribute :operate_at, :datetime, comment: '操作时间'
    attribute :send_at,    :datetime, comment: '发送时间'
    attribute :position,   :integer,  comment: '排序'
    attribute :payload,    :jsonb,    comment: '其他信息'
    attribute :option,     :jsonb,    comment: '配置信息'
    attribute :message_type, :string, comment: '消息类型'
    attribute :content,     :text,    comment: '消息内容'

    enum state: { pending: 'pending', sending: 'sending', finished: 'finished', terminated: 'terminated' }

    default_value_for(:app) { |o| o.rule&.app }
    default_value_for(:state) { 'pending' }
    default_value_for(:org) { |o| o.creator&.orgs&.first }
    default_value_for(:message_type) { 'Serve::DingtalkMessage' }

    # 生化默认的工作流
    after_create :generate_bpm_instance!, if: :require_generate_bpm_instance?
    after_update :reset_rule_latest_send_at!, if: :require_reset_rule_latest_send_at?

    after_update :generate_schedule_pack!, if: :require_generate_schedule_pack?

    delegate :name, to: :org, allow_nil: true, prefix: true
    delegate :name, to: :activity, allow_nil: true, prefix: true
    delegate :name, to: :rule, allow_nil: true, prefix: true

    action_store(
      :relate_tanent,
      :tanent,
      class_name: '::Tanent',
      action_class_name: 'Serve::TanentAction',
      alias_name: 'tanents',
      inverse_alias_name: 'serve_packs',
    )

    before_destroy :revoke_dingtalk_messages

    # 使用规则刷新候选消息
    def refresh_contents_by_rule(prompt: nil)
      # 使用ai生成内容
      contents = rule.generate_content_by_template_prompt(prompt: prompt)
      _payload = self.payload
      _payload['contents'] = contents
      update(payload: _payload)

      contents
    end

    def current_tanent
      Tanent.current || tanents.last || rule&.tanents&.last
    end

    scope :current_tanent, -> { Tanent.current ? relate_tanent_action_targets_any(Tanent.current) : all }

    scope :sreen_by_period, ->(period = 10, unit = 'week') {
      result, date = [], Date.today
      period.times do |i|
        start_time = i.try(unit).ago(date.to_datetime).try("beginning_of_#{unit}")
        end_time = i.try(unit).ago(date.to_datetime).try("end_of_#{unit}")
        result.push({
          cweek: start_time.to_date.cweek,
          start_day: start_time.to_date.strftime('%F'),
          end_of_day: end_time.to_date.strftime('%F'),
          pack_count: between_times(start_time, end_time).count
        })
      end
      result
    }

    def send_user_ids_redis_key=(redis_key)
      begin
        self.send_user_ids = ::RedisResources.get_ids_from_redis_key(redis_key)
        self.payload ||= {}
        self.payload['send_user_ids_redis_key'] = redis_key
      rescue => e
        Rails.logger.error "===send_user_ids_redis_key_error===#{e.message}"
      end
    end

    def send_user_ids_redis_key_refresh=(redis_key)
      self.send_user_ids_redis_key = redis_key
    end

    def send_user_ids_redis_key
      ::RedisResources.new(resources: self.send_users).redis_key
    end

    # 根据小项配置设置工作流
    def specific_create_workflow
      return nil unless require_generate_bpm_instance?
      workflow_id = nil
      workflow_id = rule.options.bpm.workflow_id if rule && rule.require_generate_instance?
      workflow_id = rule_item.option.bpm.workflow_id if rule_item && rule_item.require_generate_instance?
      Bpm::Workflow.find_by(id: workflow_id || ModelDefine.find_by(klass: self.class.name).model_setting_by_setable(self, flag: 'create_by_code')&.bpm_workflow_id)
    end

    def ref_model_setting_form_setting
      return nil unless rule_item
      bpm = rule_item.option.bpm
      bpm&.enabled && bpm.generate_instance && bpm.workflow_id ?
        Forms::Attr::FormSetting.init :
        (ModelDefine.find_by(klass: self.class.name).model_setting_by_setable(self, flag: 'create_by_code')&.form_setting || Forms::Attr::FormSetting.init)
    end

    def generate_bpm_instance!
      generate_create_instance(
        user: creator,
        auto_submit: true
      )
    rescue Exception => e
      Rails.logger.info e
    end

    def read_count
      messages.where(is_read: true).count
    end

    def unread_count
      messages.where(is_read: [nil, false]).count
    end

    def failed_count
      messages.where(state: 'failed').count
    end

    # 统计发送人员人数
    def send_users_count
      user_ids = payload&.dig('user_ids') || send_users || []
      user_ids.count
    end

    # 统计最后一次发送时间
    def reset_rule_latest_send_at!
      rule.update(latest_send_at: send_at)
    end

    def require_reset_rule_latest_send_at?
      saved_change_to_state? && finished? && rule
    end

    # 是否需要生成审批流
    def require_generate_bpm_instance?
      # return unless Rails.env.production?
      return true if rule.blank?
      rule_item ? rule_item.require_generate_instance? : rule.require_generate_instance?
    end

    # 是否审批后一次性生成任务
    def require_generate_schedule_pack?
      rule_record && rule_item&.is_a?(Serve::ScheduleRuleItem) && saved_change_to_state?
    end

    # 生成pack
    def generate_schedule_pack!
      rule_item.generate_schedule_pack! pack: self
    end

    # 生成浙政钉任务
    def generate_serve_message!
      user_ids = payload&.dig('user_ids') || []
      client = Dingtalk::Client.find_by(code: ENV['DINGTALK_NOTIFY_APP_CODE'])
      title =  payload&.dig('message', 'title')
      markdown = payload&.dig('message', 'markdown')
      single_title = payload&.dig('message', 'single_title') ||  '查看详情'

      app.users.where(id: user_ids).find_each do |user|
        next if self.messages.find_by(user: user) # 已经发送过消息了不在发送
        seq = SecureRandom.hex(16)
        tanent = Tanent.find_by(id: rule&.options&.website_url_tanent_id) || current_tanent
        pc_url = CGI.escape(File.join(tanent&.config&.dig('WEBSITE_URL') || ENV['WEBSITE_URL'], "/serve/home/<USER>/#{seq}"))
        mobile_url = CGI.escape(File.join(tanent&.config&.dig('WEBSITE_MOBILE_URL') || ENV['WEBSITE_MOBILE_URL'], "/#/pages/serve/messages/show/index?seq=#{seq}"))
        messsage_content = {}

        if rule.options&.message_template_enabled
          message_templates = rule.message_templates.used
          message_template = message_templates.filter{ |m| m.option.item_ids.blank? || rule_item_id.in?(m.option.item_ids) }.sample
          message_content = message_template.get_message(user: user, source: source)
          message_title =  title || message_content.dig(:card, :title) || '【廉洁提醒】'
          message_markdown = markdown || message_content.dig(:card, :content) || '您有一条廉洁提醒，请查收。'
        else
          ### 下个版本废除 ####
          message_title =  title || (rule_item ? rule_item.template_message_title : rule&.options&.template_message_title) || '【廉洁提醒】'
          # 如果使用了模版消息内容， 优先使用模版消息内容
          message_markdown = markdown || rule_item&.get_card_content(user: user, source: source) || rule&.get_card_content(user: user, source: source) || '您有一条廉洁提醒，请查收。'

          content = self.content || rule_item&.get_content(user: user, source: source) || rule&.get_content(user: user, source: source)
        end

        message = {
          msgtype: 'action_card',
          action_card: {
            title: message_title,
            markdown: message_markdown,
            single_title: single_title,
            single_pc_url: "taurus://taurusclient/action/open_app?corpid=#{client.corp_id}&container_type=work_platform&app_id=#{ENV['DINGTALK_NOTIFY_APP_CODE']}&redirect_type=jump&redirect_url=#{pc_url}",
            single_url: "taurus://taurusclient/action/open_app?type=1&offline=false&url=#{mobile_url}"
          }
        }

        messages.create!(
          seq: seq,
          type: message_type,
          activity: self.activity,
          notifyable: self,
          content: content,
          user: user,
          sender: creator,
          body: message,
          payload: message_content
        )

      rescue Exception => e
        Rails.logger.info "===error_generate_message===#{user.id}"
      end
    end

    def revoke_dingtalk_messages
      messages.each do |message|
        if message.respond_to?(:revoke_dingtalk_messages)
          # 调用Message的revoke_dingtalk_messages方法
          message.send(:revoke_dingtalk_messages)
        end
      end
    end

    # 撤回并销毁指定的消息
    # @param message_ids [Array<Integer>] 要撤回并销毁的消息ID数组
    # @return [Integer] 成功销毁的消息数量
    def revoke_and_destroy_messages(message_ids)
      return 0 if message_ids.blank?

      target_messages = messages.where(id: message_ids)
      destroyed_count = 0

      target_messages.each do |message|
        if message.respond_to?(:revoke_dingtalk_messages)
          # 调用Message的revoke_dingtalk_messages方法
          message.send(:revoke_dingtalk_messages)
        end

        if message.destroy
          destroyed_count += 1
        end
      end

      destroyed_count
    end

    def generate_ai_message!
      ai_message = Serve::AiMessage.new(
        creator: creator,
        pack: self,
        content: content,
        option: option,
      )
    end
  end
end
