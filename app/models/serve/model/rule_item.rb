module Serve::Model::RuleItem
  extend ActiveSupport::Concern

  included do
    self.track_migration = true
    stiable

    belongs_to :app
    belongs_to :rule

    has_many :packs, dependent: :nullify

    class Option
      include AttrJson::Model
      attr_json_config(bad_cast: :as_nil, unknown_key: :allow)

      class Scope
        include AttrJson::Model
        attr_json_config(bad_cast: :as_nil, unknown_key: :allow)

        attr_json :key, :string
        attr_json :val, :string
        attr_json :scope_type, :string  # 检索方式 ransack 和 class_method
        attr_json :method, :string
      end

      # 关联的对象
      class Record
        include AttrJson::Model
        attr_json_config(bad_cast: :as_nil, unknown_key: :allow)

        attr_json :enabled, :boolean, default: false
        attr_json :klass, :string
        attr_json :scopes, Scope.to_type, array: true, default: []
      end

      # 素材配置
      class Activity
        include AttrJson::Model
        attr_json_config(bad_cast: :as_nil, unknown_key: :allow)

        attr_json :enabled, :boolean, default: false
        attr_json :scopes, Scope.to_type, array: true, default: []
      end

      # 发送人员配置
      class User
        include AttrJson::Model
        attr_json_config(bad_cast: :as_nil, unknown_key: :allow)

        attr_json :enabled, :boolean, default: false
        attr_json :scopes, Scope.to_type, array: true, default: []
      end

      # 关联对象发送人员配置
      class RelateUser
        include AttrJson::Model
        attr_json_config(bad_cast: :as_nil, unknown_key: :allow)

        attr_json :enabled, :boolean, default: false
        attr_json :method, :string
        attr_json :args, :string
      end

      # 模版消息配置
      class Message
        include AttrJson::Model
        attr_json_config(bad_cast: :as_nil, unknown_key: :allow)

        # 浙政钉消息模版-用户收到的消息
        attr_json :enabled, :boolean, default: false
        attr_json :template_message_title, :string
        attr_json :template_message_body, :string
        attr_json :template_message_body_scopes, Scope.to_type, array: true, default: []
        # 浙政钉消息点击详情 例如 发送(【姓名 + 职务】您好,您的招标项目【项目名称】)
        attr_json :template_message, :string
        attr_json :templates, Scope.to_type, array: true, default: []
      end

      class Bpm
        include AttrJson::Model
        attr_json_config(bad_cast: :as_nil, unknown_key: :allow)

        attr_json :enabled, :boolean, default: false
        attr_json :generate_instance, :boolean, default: true
        attr_json :workflow_id, :integer
      end

      class Schedule
        include AttrJson::Model
        attr_json_config(bad_cast: :as_nil, unknown_key: :allow)

        attr_json :enabled, :boolean, default: false
        attr_json :period, :integer
        attr_json :unit, :string, default: "month"
      end

      class Dingtalk
        include AttrJson::Model
        attr_json_config(bad_cast: :as_nil, unknown_key: :allow)

        attr_json :enabled, :boolean, default: false
        attr_json :client_id, :integer
      end

      attr_json :record, Record.to_type, default: {}
      attr_json :user, User.to_type, default: {}
      attr_json :activity, Activity.to_type, default: {}
      attr_json :message, Message.to_type, default: {}
      attr_json :relate_user, RelateUser.to_type, default: {}
      attr_json :schedule, Schedule.to_type, default: {}
      attr_json :bpm, Bpm.to_type, default: {}
      attr_json :send_type, :string, default: "direct"
      attr_json :client_id, :integer
    end

    attribute :name, :string, comment: "名称"
    attribute :state, :string, comment: "状态"
    attribute :uuid, :string, comment: "唯一标识号"
    attribute :mode, :string, comment: "标识"
    attribute :option, :jsonb, comment: "配置"
    attribute :payload, :jsonb, comment: "其他字段"
    serialize :option, coder: Option.to_serialization_coder, default: {}

    enum state: { used: "used", todo: "todo", closed: "closed" }

    default_value_for(:app) { |o| o.rule&.app }
    default_value_for(:state) { "used" }
    default_value_for(:uuid) { SecureRandom.hex(16) }

    action_store(
      :relate,
      :activity,
      class_name: "Serve::Activity",
      action_class_name: "Serve::RuleAction",
      alias_name: "activities",
      inverse_alias_name: "rules_items",
    )

    # 根据配置匹配关联的对象
    def relate_records
      klass = option.record.klass.constantize
      association = klass.all
      option.record.scopes.each do |scope|
        if scope.scope_type == "ransack"
          val = scope.key&.end_with?("_in") || scope.key&.end_with?("_any") ? scope.val.split(",") : scope.val
          association = association.ransack({ scope.key => val }).result
        else
          association = association.try(scope.method, *scope.val.to_s.split(","))
        end
      end

      # 暴露出方法提供给用户自行
      klass.respond_to?(:find_records_by_rule) ?
        klass.find_records_by_rule(rule: self) :
        association.ransack(org_id_in: rule.all_related_orgs.map(&:id)).result
    end

    def query_relate_users(source: nil)
      return [] unless source && option.relate_user.enabled

      begin
        method_name = option.relate_user.method
        args = option.relate_user.args.to_s.split(",").map(&:strip)

        Rails.logger.info "调用 #{source.class}##{method_name} 方法，参数: #{args.inspect}"
        result = if args.any?
            source.public_send(method_name, *args)
          else
            source.public_send(method_name)
          end

        Rails.logger.info "#{source.class}##{method_name} 返回: #{result.inspect}"
        Array.wrap(result)
      rescue => e
        Rails.logger.error "调用 #{source.class}##{method_name} 方法失败: #{e.message}"
        []
      end
    end

    # 根据配置匹配要发送的人
    def query_users
      # 如果没有启用则返回空
      return app.users.none unless option.user.enabled

      association = app.users
      option.user.scopes.each do |scope|
        if scope.scope_type == "ransack"
          val = scope.key&.end_with?("_in") || scope.key&.end_with?("cont_any") ? scope.val.split(",") : scope.val
          association = association.ransack({ scope.key => val }).result
        else
          association = association.try(scope.method, *scope.val.to_s.split(",").unshift(self.rule))
        end
      end
      # association = association.ransack(org_id_in: rule.all_related_orgs.map(&:id)).result if rule.org_relations.count > 0
      association
    end

    # 根据配置匹配到要发送的素材
    def query_activities
      # 如果没有启用则返回空
      return app.serve_activities.none unless option.activity.enabled

      association = app.serve_activities
      option.activity.scopes.each do |scope|
        if scope.scope_type == "ransack"
          val = scope.key&.end_with?("_in") || scope.key&.end_with?("cont_any") ? scope.val.split(",") : scope.val
          association = association.ransack({ scope.key => val }).result
        else
          association = association.try(scope.method, *scope.val.to_s.split(","))
        end
      end
      association
    end

    def relate?
      option.send_type == "relate"
    end

    def generate_pack!(rule_record: nil)
      relate? ? generate_relate_pack!(rule_record: rule_record) : generate_direct_pack!(rule_record: rule_record)
    end

    # 关联对象发送
    def generate_relate_pack!(rule_record: nil)
      relate_records.find_each do |record|
        generate_system_pack! org: record.try(:org), source: record, rule_record: rule_record
      end
    end

    # 直接发送
    def generate_direct_pack!(rule_record: nil)
      rule.all_related_orgs.each do |org|
        generate_system_pack! org: org, rule_record: rule_record
      end
    end

    # 系统生成pack
    def generate_system_pack!(org: nil, source: nil, rule_record: nil)
      Rails.logger.info "开始生成Pack，源对象: #{source&.class}##{source&.id}"

      # 优先使用指定素材
      activity_ids = self.activity_ids.presence || rule.activity_ids.presence
      activity_ids = query_activities.pluck(:id) unless activity_ids
      Rails.logger.info "活动ID: #{activity_ids.inspect}"

      # 关联对象要发送的人
      user_ids = if relate?
          Rails.logger.info "查询关联用户，source: #{source&.inspect}"
          users = query_relate_users(source: source)
          Rails.logger.info "查询到的关联用户: #{users.inspect}"
          users
        else
          users = query_users.pluck(:id)
          Rails.logger.info "查询到的用户: #{users.inspect}"
          users
        end
      user_ids = user_ids.uniq.compact
      Rails.logger.info "处理后的用户ID: #{user_ids.inspect}"

      if user_ids.blank?
        Rails.logger.warn "没有可用的用户ID，退出生成Pack"
        return nil
      end

      activity = app.serve_activities.find_by(id: activity_ids.sample)
      rule.packs.create!(
        rule_record: rule_record,
        rule_item: self,
        name: ["【#{rule_record&.schedule_offset_at&.strftime("%F")}】", rule.name] * "",
        send_at: rule_record&.schedule_offset_at,
        activity: activity,
        period: rule_record.period,
        org: org,
        creator: rule.creator,
        message_type: rule.message_type,
        state: require_generate_instance? ? "pending" : "sending",
        source: source,
        tanent_ids: rule.tanent_ids,
        payload: {
          user_ids: user_ids,
          activity_ids: activity_ids.sample(20),
        },
      )
    end

    # 发送消息配置 卡片标题
    def template_message_title
      option.message.enabled ? option.message.template_message_title : rule.options.template_message_title
    end

    # 发送消息配置 卡片内容
    def template_message_body_scopes
      scopes = option.message.enabled ? option.message.template_message_body_scopes : rule.options&.template_message_body_scopes
      scopes || []
    end

    def template_message_body
      option.message.enabled ? option.message.template_message_body : rule.options.template_message_body.presence
    end

    # 发送消息配置 信息点击详情
    def templates
      scopes = option.message.enabled ? option.message.templates : rule.options&.templates
      scopes || []
    end

    def template_message
      option.message.enabled ? option.message.template_message : rule.options&.template_message
    end

    def require_generate_instance?
      option.bpm&.enabled ? option.bpm.generate_instance : false
    end

    # 关联的浙政钉id
    def client_id
    end

    # 根据pack生成新的pack 例如新人入职需要连续
    def generate_pack_from_pack!(pack: nil)
      # 优先使用指定素材
      activity_ids = self.activity_ids.presence || rule.activity_ids.presence
      activity_ids = query_activities.pluck(:id) unless activity_ids

      activity = app.serve_activities.find_by(id: activity_ids.sample)
      send_at = option.schedule.period.try(option.schedule.unit).since(pack.send_at)
      rule.packs.create!(
        rule_record: pack.rule_record,
        rule_item: self,
        name: ["【#{send_at&.strftime("%F")}】", rule.name] * "",
        send_at: send_at,
        activity: activity,
        period: pack.period,
        org: pack.org,
        creator: rule.creator,
        message_type: rule.message_type,
        state: "sending",
        source: pack.source,
        tanent_ids: pack.tanent_ids,
        payload: {
          user_ids: pack.payload.dig("user_ids"),
          activity_ids: activity_ids.sample(20),
        },
      )
    end

    # 提供ai使用
    def generate_ai_pack!(user_ids: [], activity_ids: [], send_at: nil, source: nil, org: nil)
      # 优先使用指定素材
      activity_ids = activity_ids.presence || self.activity_ids.presence || rule.activity_ids.presence
      activity_ids = query_activities.pluck(:id) unless activity_ids

      # 关联对象要发送的人
      if user_ids.blank?
        user_ids = relate? ? query_relate_users(source: source) : query_users.pluck(:id) # 关联对象要发送的用户
      end
      user_ids = user_ids.uniq.compact

      return nil if user_ids.blank?
      activity = app.serve_activities.find_by(id: activity_ids.sample)

      rule.packs.create!(
        rule_record: rule_record,
        rule_item: self,
        name: ["【#{rule_record&.schedule_offset_at&.strftime("%F")}】", rule.name] * "",
        send_at: send_at,
        activity: activity,
        org: org,
        creator: rule.creator,
        message_type: rule.message_type,
        state: rule.options&.generate_instance ? "pending" : "sending",
        source: source,
        tanent_ids: rule.tanent_ids,
        payload: {
          user_ids: user_ids,
          activity_ids: activity_ids.sample(20),
        },
      )
    end

    def get_card_content(user: nil, source: nil)
      return nil unless option.message&.enabled
      return nil if option.message.template_message_body.blank?

      scopes = option.message.template_message_body_scopes || []
      data = scopes.each_with_object({}) do |t, h|
        h[t.val.to_sym] = t.key == "user" ? user.try_method(t.method) : source.try_method(t.method)
      end
      format(option.message.template_message_body, data)
    end

    def get_content(user: nil, source: nil)
      return nil unless option.message&.enabled
      return nil if option.message&.template_message.blank?

      scopes = option&.message&.templates
      data = scopes.each_with_object({}) do |t, h|
        h[t.val.to_sym] = t.key == "user" ? user.try_method(t.method) : source.try_method(t.method)
      end
      format(option.message.template_message, data)
    end
  end

  class_methods do
  end
end
