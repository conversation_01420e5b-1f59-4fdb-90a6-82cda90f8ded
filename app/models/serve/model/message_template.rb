module Serve::Model::MessageTemplate
  extend ActiveSupport::Concern

  included do
    self.track_migration = true
    stiable

    belongs_to :app
    belongs_to :rule,      optional: true
    belongs_to :rule_item, optional: true

    class Option
      include AttrJson::Model
      attr_json_config(bad_cast: :as_nil, unknown_key: :allow)

      class Scope
        include AttrJson::Model
        attr_json_config(bad_cast: :as_nil, unknown_key: :allow)

        attr_json :key,           :string
        attr_json :val,           :string
        attr_json :scope_type,    :string  # 检索方式 ransack 和 class_method
        attr_json :method,        :string
      end

      # 模版消息配置
      class Message
        include AttrJson::Model
        attr_json_config(bad_cast: :as_nil, unknown_key: :allow)

        # 浙政钉消息模版-用户收到的消息
        attr_json :enabled, :boolean, default: false
        attr_json :template_message_title,   :string
        attr_json :template_message_body,    :string
        attr_json :template_message_body_scopes,  Scope.to_type, array: true, default: []
        attr_json :template_message,   :string
        attr_json :templates, Scope.to_type, array: true, default: []
      end

      # 图库设置
      class Image
        include AttrJson::Model
        attr_json_config(bad_cast: :as_nil, unknown_key: :allow)

        attr_json :enabled, :boolean, default: false
        attr_json :files, ActiveModel::Type::Value.new, default: []
      end

      class ContentTemplate
        include AttrJson::Model
        attr_json_config(bad_cast: :as_nil, unknown_key: :allow)

        attr_json :title,    :string, default: false
        attr_json :state,    :string, default: 'used'
        attr_json :content,  :string
        attr_json :scopes, Scope.to_type, array: true, default: []
      end

      # 卡片内容
      class CardContent
        include AttrJson::Model
        attr_json_config(bad_cast: :as_nil, unknown_key: :allow)

        attr_json :enabled,   :boolean, default: false
        attr_json :templates, ContentTemplate.to_type, array: true, default: []
      end

      # 浏览内容
      class Content
        include AttrJson::Model
        attr_json_config(bad_cast: :as_nil, unknown_key: :allow)

        attr_json :enabled,   :boolean, default: false
        attr_json :templates, ContentTemplate.to_type, array: true, default: []
      end

      # 素材信息 暂时只支持固定素材
      class Activity
        include AttrJson::Model
        attr_json_config(bad_cast: :as_nil, unknown_key: :allow)

        attr_json :enabled,      :boolean, default: false
        attr_json :activity_ids, :integer, array: true, default: []
      end

      attr_json :template_key, :string
      attr_json :item_ids, :integer, array: true, default: []
      attr_json :image,         Image.to_type,        default: {}
      attr_json :card_content,  CardContent.to_type,  default: {}
      attr_json :content,       Content.to_type,      default: {}
      attr_json :activity,      Activity.to_type,      default: {}
    end

    attribute :name,    :string, comment: '名称'
    attribute :state,   :string, comment: '状态'
    attribute :payload, :jsonb,  comment: '其他数据'
    attribute :option,  :jsonb,  comment: '配置'
    serialize :option,  coder: Option.to_serialization_coder, default: {}

    enum state: { pending: 'pending', used: 'used', closed: 'closed' }
    default_value_for(:state){ 'used' }
    default_value_for(:app){ |o| o.rule&.app || o.rule_item&.app }

    # 获取图片
    def get_image
      url = option.image.enabled ? option.image.files&.sample&.dig('url') : nil
      { url: url}
    end

    # 获取卡片内容
    def get_card_content(user: nil, source: nil)
      message = { title: nil, content: nil }
      return message unless option.card_content.enabled
      # 查找可用的模版
      templates = option.card_content.templates.filter{ |template| template.state == 'used' }
      template = templates.sample

      # 根据配置完善卡片内容
      if template.present?
        message[:title] = template.title
        if template.scopes.present?
          data = template.scopes.each_with_object({}) do |t, h|
                    h[t.val.to_sym] = t.key == 'user' ? user.try_method(t.method) : source.try_method(t.method)
                  end
          message[:content] = format(template.content, data)
        end
      end

      message
    end

    # 获取信息内容
    def get_content user: nil, source: nil
      message = { title: nil, content: nil }
      return message unless option.content.enabled
      # 查找可用的模版
      templates = option.content.templates.filter{ |template| template.state == 'used' }
      template = templates.sample

      # 根据配置完善卡片内容
      if template.present?
        message[:title] = template.title
        if template.scopes.present?
          data = template.scopes.each_with_object({}) do |t, h|
                    h[t.val.to_sym] = t.key == 'user' ? user.try_method(t.method) : source.try_method(t.method)
                  end
          message[:content] = format(template.content, data)
          message[:template] = template
        end
      end

      message
    end

    def get_activity
      option.activity.enabled ? option.activity.activity_ids.sample : []
    end

    def get_message user: nil, source: nil
      {
        key: option.template_key,
        image: get_image,
        card: get_card_content(user: user, source: source),
        content: get_content(user: user, source: source),
        activity_id: get_activity
      }
    end
  end
end
