Rails.application.routes.draw do
  namespace :serve do
    namespace :manage do
      resources :rule_groups
      resources :rules
      resources :rule_items
      resources :packs do
        resources :send_users, only: [:index]
        resources :receivers
      end
      resources :origins
      resources :messages do
        get :lite_index, on: :collection
        post :revoke, on: :member
      end
      resources :ai_messages
      resources :ai_message_squares, only: [:index, :show] do
        post :clone_to_template, on: :member
        markable
      end
      resources :ai_message_templates
      resources :orgs, only: [:index, :show]
    end

    namespace :user do
      resources :rules, only: [:show] do
        post :generate_content_by_template_prompt, on: :member
      end
      resources :messages, only: [:index, :show]
      resources :packs, only: [:show] do
        post :refresh_contents_by_rule, on: :member
      end
    end
  end

  namespace :iest do
    namespace :manage do
      resources :apps, only: []  do
        post :ta_resource_statistic, on: :member
        post :ta_collection_statistic, on: :collection
      end
      resources :tags, only: [:index, :show]
      resources :orgs, only: [:index, :show]
      resources :paperworks
    end
  end

  namespace :iest do
    namespace :ai do
      namespace :chat do
        namespace :user do
          resources :conversations do
            resources :messages
            resources :mentions
            resources :mention_versions, only: [:show]
          end
        end
      end
    end

    namespace :ai_notify do
      namespace :manage do
        resources :messages, only: [:create]
      end
    end
  end
end
