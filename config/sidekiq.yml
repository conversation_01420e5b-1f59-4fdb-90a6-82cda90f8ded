:concurrency: 15
:logfile: ./log/sidekiq.log
:pidfile: ./.pids/sidekiq.pid
:queues:
  - default
:max_retries: 2
:scheduler:
  :schedule:
    serve_rule:
      cron: '* */1 * * *'   # 每小时执行一次
      class: 'Serve::RuleScheduler'
      description: '每天检查发送任务的规则'
    serve_pack:
      cron: '*/1 * * * *'   # 1分钟执行一次
      class: 'Serve::PackScheduler'
      description: '检查是否有pack需要发送'
    serve_activity:
      cron: '0 6 * * *'   # 每天6点检查
      class: 'Serve::ActivityScheduler'
      description: '每天爬取网站'
    clean_duplicate_activity:
      cron: '0 4 * * *'   # 每天4点检查
      class: 'CleanDuplicateActivity'
      description: '每天检查重复的素材，删除'
    serve_bid_project:
      cron: '0 5 * * *'   # 每天1点检查
      class: 'Serve::BidProjectScheduler'
      description: '同步招投标'
    serve_birthday:
      cron: '0 7 * * 1'   # 每周一早上7点
      class: 'Serve::BirthdayScheduler'
      description: '寻找本周和下周是否有人生日或者政治生日'
    check_token:
      cron: '*/5 * * * *'
      class: 'CheckToken'
      description: '健康检查免登录token是否失效'
    sync_org_and_member:
      cron: '0 0 * * 1'
      class: 'SyncOrgAndMember'
      description: '同步组织和人员'
    bidding_pack_retry_ding_message:
      cron: '30 9 * * *'
      class: 'BiddingPackRetryDingMessage'
      description: '招投标信息超过时限则使用ding消息再次提醒'
    # one_day_later_ding_message:
    #   cron: '0 9-17 * * *'
    #   class: 'OneDayLaterDingMessage'
    #   description: '未读消息超过24小时则使用ding消息再次提醒'
    check_ding_message:
      cron: '*/5 * * * *'
      class: 'CheckDingMessage'
      description: '将ding消息的已读情况同步到message里'
    # sync_serve_tags_scheduler:
    #   cron: '0 3 * * *'
    #   class: 'SyncServeTagsScheduler'
    #   description: '每天全素材库进行标签匹配'
