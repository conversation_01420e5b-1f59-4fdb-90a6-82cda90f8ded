source "https://gems.ruby-china.com"
# source 'https://mirrors.aliyun.com/rubygems/'
git_source(:tallty) { |repo| "******************:#{repo}.git" }

# Bundle edge Rails instead: gem "rails", github: "rails/rails", branch: "main"
gem "rails", "~> 7.1.0"
# Use sqlite3 as the database for Active Record
gem "sqlite3", ">= 1.4"
# Use the Puma web server [https://github.com/puma/puma]
gem "puma", ">= 5.0"
# Build JSON APIs with ease [https://github.com/rails/jbuilder]
# gem "jbuilder"
# Use Redis adapter to run Action Cable in production
# gem "redis", ">= 4.0.1"

# Use Kredis to get higher-level data types in Redis [https://github.com/rails/kredis]
# gem "kredis"

# Use Active Model has_secure_password [https://guides.rubyonrails.org/active_model_basics.html#securepassword]
# gem "bcrypt", "~> 3.1.7"

# Windows does not include zoneinfo files, so bundle the tzinfo-data gem
gem "tzinfo-data", platforms: [:windows, :jruby]

# Reduces boot times through caching; required in config/boot.rb
gem "bootsnap", require: false

# Use Active Storage variants [https://guides.rubyonrails.org/active_storage_overview.html#transforming-images]
# gem "image_processing", "~> 1.2"

# Use Rack CORS for handling Cross-Origin Resource Sharing (CORS), making cross-origin Ajax possible
# gem "rack-cors"

group :development, :test do
  # See https://guides.rubyonrails.org/debugging_rails_applications.html#debugging-with-the-debug-gem
  gem "debug", platforms: [:mri, :windows], require: "debug/prelude"

  # Static analysis for security vulnerabilities [https://brakemanscanner.org/]
  gem "brakeman", require: false

  # Omakase Ruby styling [https://github.com/rails/rubocop-rails-omakase/]
  gem "rubocop-rails-omakase", require: false
end

gem "acts_as_pasting", tallty: "open-source/acts_as_pasting"
gem "dotenv-rails", require: "dotenv/load"
gem "mina", require: false
gem "mina-multistage", require: false
gem "mini_magick"
gem "mysql2"
gem "neighbor"
gem "pg"
gem "pry-byebug"
gem "rubocop", require: false
gem "rubocop-performance"
gem "rubocop-rails"
gem "sidekiq", "~>6.5.7"
gem "sidekiq-scheduler"
gem "simple_controller", tallty: "open-source/simple_controller"
gem "sinatra", require: false
gem "sprockets"
gem "tallty_import_export", tallty: "open-source/tallty_import_export"

gem "selenium-webdriver"
gem "webdrivers"

gem "faraday"
gem "pdf-reader"
gem "pgvector"
gem "ruby-openai"
gem "sequel"

gem "rails_action_core", tallty: "ta-rails/rails_action_core"
gem "rails_assessment", tallty: "ta-rails/rails_assessment"
gem "rails_audit", tallty: "ta-rails/rails_audit"
gem "rails_bot", tallty: "ta-llm/rails_bot"
gem "rails_bpm", tallty: "ta-rails/rails_bpm"
gem "rails_chat", tallty: "ta-rails/rails_chat"
gem "rails_com", tallty: "ta-rails/rails_com"
gem "rails_data", tallty: "ta-rails/rails_data"
gem "rails_dingtalk", tallty: "ta-rails/rails_dingtalk"
gem "rails_favor", tallty: "ta-rails/rails_favor"
gem "rails_grant", tallty: "ta-rails/rails_grant"
gem "rails_iest", path: "../rails_iest"
gem "rails_notify", tallty: "ta-rails/rails_notify"
gem "rails_opm", tallty: "ta-rails/rails_opm"
gem "rails_permit", tallty: "ta-rails/rails_permit"
gem "rails_reg", tallty: "ta-rails/rails_reg"
gem "rails_region", tallty: "ta-rails/rails_region"
gem "rails_res", tallty: "ta-rails/rails_res"
gem "rails_rss", tallty: "ta-rails/rails_rss"
gem "rails_schedule", tallty: "ta-rails/rails_schedule"
gem "rails_serve", tallty: "ta-rails/rails_serve"
gem "rails_sms_auth", tallty: "ta-rails/rails_sms_auth"
gem "rails_soa_auth", tallty: "ta-rails/rails_soa_auth"
gem "rails_spider", tallty: "ta-rails/rails_spider"
gem "rails_tofu", tallty: "ta-rails/rails_tofu"

group :development, :test do
  gem "annotate"
  gem "factory_bot_rails"
  gem "pry-rails"
  gem "rspec-rails"
  gem "rspec-rails-swagger", tallty: "open-source/rspec-rails-swagger"
  gem "sassc"
  gem "shoulda-matchers"
  gem "simplecov", require: false
end
